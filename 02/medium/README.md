# 🟡 中等难度 - 部分框架

## 适合对象
- 有一定Python基础的学生
- 能够独立思考和解决问题的学习者
- 希望提升编程技能的中级学生

## 文件说明

### 📁 currency_api_medium.py
**汇率API使用 - 类和方法实现**
- **学习重点**: 面向对象编程和API数据处理
- **需要实现**: 5个核心方法 + 交互界面
- **预计时间**: 45-60分钟

**主要挑战**:
- 设计CurrencyConverter类的方法
- 实现错误处理和数据验证
- 创建用户友好的交互界面
- 处理历史数据的日期计算

### 📁 matplotlib_medium.py
**Matplotlib可视化 - 图表创建器**
- **学习重点**: 多种图表类型和数据可视化
- **需要实现**: 6个可视化方法 + 数据生成
- **预计时间**: 50-70分钟

**主要挑战**:
- 创建专业级别的图表
- 实现图表美化和自定义
- 处理多维数据可视化
- 设计交互式图表创建器

## 技能要求

### 必备技能
- Python基础语法（变量、函数、类）
- 基本的数据结构（列表、字典）
- 简单的循环和条件语句
- 基础的异常处理概念

### 将要学习的技能
- 面向对象编程（类和方法）
- HTTP请求和JSON数据处理
- 日期时间处理
- 数据可视化技术
- 用户交互界面设计

## 实现指南

### 第一阶段：理解框架
1. **阅读代码结构**: 理解类的设计和方法的作用
2. **分析TODO任务**: 明确每个方法需要实现的功能
3. **查看提示信息**: 利用注释中的实现提示

### 第二阶段：逐步实现
1. **从简单开始**: 先实现validate_currency()等简单方法
2. **测试验证**: 每实现一个方法就测试一次
3. **逐步完善**: 添加错误处理和边界情况处理

### 第三阶段：整合测试
1. **功能测试**: 确保所有方法都能正常工作
2. **交互测试**: 测试用户交互界面
3. **异常测试**: 测试各种异常情况的处理

## 实现提示

### currency_api_medium.py 关键点

#### 1. get_current_rate() 方法
```python
# 基本结构示例
def get_current_rate(self, from_currency, to_currency):
    url = f"{self.base_url}/latest?from={from_currency}&to={to_currency}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            return data["rates"][to_currency]
    except Exception as e:
        print(f"错误: {e}")
        return None
```

#### 2. 日期处理技巧
```python
from datetime import datetime, timedelta
start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
```

### matplotlib_medium.py 关键点

#### 1. 图表美化技巧
```python
plt.figure(figsize=(10, 6))
plt.plot(x, y, color='blue', linewidth=2, marker='o', markersize=6)
plt.grid(True, alpha=0.3)
plt.tight_layout()
```

#### 2. 子图布局
```python
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
axes[0, 0].plot(x1, y1)
axes[0, 1].bar(x2, y2)
```

## 评估标准

### 功能完整性 (40%)
- [ ] 所有方法都能正常运行
- [ ] 能够处理正常的输入数据
- [ ] 交互界面友好易用

### 代码质量 (30%)
- [ ] 代码结构清晰，逻辑合理
- [ ] 适当的注释和文档
- [ ] 遵循Python编码规范

### 错误处理 (20%)
- [ ] 能够处理网络请求失败
- [ ] 能够处理无效的用户输入
- [ ] 提供有意义的错误信息

### 创新性 (10%)
- [ ] 添加额外的功能特性
- [ ] 改进用户体验
- [ ] 代码优化和性能提升

## 常见问题解决

### Q: 网络请求总是失败
A: 检查网络连接，可以先用浏览器测试API是否可访问

### Q: 图表显示不正常
A: 确保matplotlib正确安装，检查数据格式是否正确

### Q: 日期计算出错
A: 注意datetime和字符串格式的转换，使用strftime()格式化日期

### Q: 类方法不知道怎么实现
A: 先实现最基本的功能，再逐步添加错误处理和优化

## 扩展挑战
完成基础实现后，可以尝试：
1. 添加更多货币支持
2. 实现汇率预测功能
3. 添加图表导出功能
4. 创建Web界面
5. 添加数据缓存机制
