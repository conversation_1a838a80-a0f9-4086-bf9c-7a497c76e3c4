"""
🟡 中等难度：Matplotlib数据可视化
适合有一定Python基础的学生

学习目标：
1. 独立创建多种类型的图表
2. 掌握图表美化和自定义
3. 学会处理真实数据并可视化

任务：根据注释提示，完成以下函数的实现
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta

class ExchangeRateVisualizer:
    """汇率数据可视化器"""
    
    def __init__(self):
        # 设置中文字体支持（如果需要）
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    
    def create_line_chart(self, dates, rates, title, currency_pair):
        """
        创建汇率趋势线图
        
        参数:
            dates (list): 日期列表
            rates (list): 汇率列表
            title (str): 图表标题
            currency_pair (str): 货币对，如 "JPY/CNY"
            
        TODO: 实现以下功能
        1. 创建线图并设置样式（颜色、线宽、标记点）
        2. 设置标题和坐标轴标签
        3. 添加网格和图例
        4. 美化图表（设置图表大小、背景色等）
        5. 显示图表
        """
        # 提示：使用 plt.figure(figsize=(10, 6)) 设置图表大小
        # 提示：使用 plt.plot() 的参数如 color, linewidth, marker 等
        pass
    
    def create_bar_chart(self, currencies, rates, base_currency):
        """
        创建多货币汇率对比柱状图
        
        参数:
            currencies (list): 货币代码列表
            rates (list): 对应的汇率列表
            base_currency (str): 基础货币
            
        TODO: 实现以下功能
        1. 创建柱状图并设置颜色
        2. 添加数值标签（在每个柱子上显示具体数值）
        3. 设置标题和坐标轴标签
        4. 旋转x轴标签以避免重叠
        5. 调整布局并显示
        """
        # 提示：使用 plt.bar() 创建柱状图
        # 提示：使用 plt.text() 或 ax.text() 添加数值标签
        pass
    
    def create_comparison_chart(self, amounts, currency_data):
        """
        创建不同金额的货币转换对比图
        
        参数:
            amounts (list): 金额列表
            currency_data (dict): 货币数据，格式为 {"货币名": [转换后金额列表]}
            
        TODO: 实现以下功能
        1. 创建多条线的对比图
        2. 为每条线设置不同的颜色和样式
        3. 添加图例说明
        4. 设置合适的坐标轴范围
        5. 添加标题和标签
        """
        # 提示：使用多次 plt.plot() 绘制多条线
        # 提示：使用 plt.legend() 添加图例
        pass
    
    def create_pie_chart(self, portfolio_data):
        """
        创建投资组合饼图
        
        参数:
            portfolio_data (dict): 投资组合数据，格式为 {"货币": 金额}
            
        TODO: 实现以下功能
        1. 创建饼图并设置颜色
        2. 显示百分比和标签
        3. 突出显示最大的部分
        4. 添加标题
        5. 调整图表布局
        """
        # 提示：使用 plt.pie() 创建饼图
        # 提示：使用 autopct 参数显示百分比
        pass
    
    def create_subplot_dashboard(self, data_dict):
        """
        创建多图表仪表板
        
        参数:
            data_dict (dict): 包含各种数据的字典
            
        TODO: 实现以下功能
        1. 创建2x2的子图布局
        2. 在每个子图中绘制不同类型的图表
        3. 设置每个子图的标题
        4. 调整子图间距
        5. 添加总标题
        """
        # 提示：使用 plt.subplots(2, 2, figsize=(15, 10))
        # 提示：使用 ax[i, j] 访问特定的子图
        pass

def generate_sample_data():
    """
    生成示例数据用于演示
    
    TODO: 创建以下示例数据
    1. 最近7天的日期列表
    2. 对应的汇率数据（可以使用随机数模拟）
    3. 多种货币的汇率对比数据
    4. 投资组合数据
    
    返回:
        dict: 包含所有示例数据的字典
    """
    # 提示：使用 datetime 和 timedelta 生成日期
    # 提示：使用 numpy.random 生成模拟汇率数据
    pass

def interactive_chart_creator():
    """
    交互式图表创建器
    
    TODO: 实现用户交互功能
    1. 显示可用的图表类型菜单
    2. 根据用户选择创建对应的图表
    3. 允许用户自定义图表参数
    4. 提供保存图表的选项
    """
    visualizer = ExchangeRateVisualizer()
    
    print("📊 交互式图表创建器")
    print("可用的图表类型：")
    print("1. 汇率趋势线图")
    print("2. 多货币对比柱状图")
    print("3. 金额转换对比图")
    print("4. 投资组合饼图")
    print("5. 综合仪表板")
    
    # TODO: 实现交互逻辑
    pass

def main():
    """
    主函数 - 演示所有图表类型
    
    TODO: 创建完整的演示程序
    1. 生成示例数据
    2. 创建各种类型的图表
    3. 展示图表自定义功能
    4. 启动交互式图表创建器
    """
    print("🎯 Matplotlib数据可视化演示")
    print("=" * 50)
    
    # TODO: 实现演示逻辑
    pass

if __name__ == "__main__":
    main()

"""
💡 实现提示：

1. create_line_chart() 函数：
   - plt.figure(figsize=(10, 6))
   - plt.plot(dates, rates, color='blue', linewidth=2, marker='o')
   - plt.title(), plt.xlabel(), plt.ylabel()
   - plt.grid(True, alpha=0.3)

2. create_bar_chart() 函数：
   - plt.bar(currencies, rates, color=['red', 'blue', 'green'])
   - for i, v in enumerate(rates): plt.text(i, v, str(v))
   - plt.xticks(rotation=45)

3. create_comparison_chart() 函数：
   - 使用循环为每种货币绘制一条线
   - 设置不同的 color 和 linestyle
   - plt.legend(currency_data.keys())

4. create_pie_chart() 函数：
   - plt.pie(values, labels=labels, autopct='%1.1f%%')
   - 使用 explode 参数突出显示

5. create_subplot_dashboard() 函数：
   - fig, axes = plt.subplots(2, 2, figsize=(15, 10))
   - axes[0, 0].plot(), axes[0, 1].bar() 等
   - plt.tight_layout()

🎯 预期功能：
- 能够创建多种类型的专业图表
- 具备图表美化和自定义能力
- 能够处理和可视化复杂数据
- 提供交互式图表创建功能
- 支持图表保存和导出
"""
