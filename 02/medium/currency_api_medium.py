"""
🟡 中等难度：汇率API使用
适合有一定Python基础的学生

学习目标：
1. 独立实现API数据获取和处理
2. 掌握错误处理和数据验证
3. 学会设计用户交互界面

任务：根据注释提示，完成以下函数的实现
"""

import requests
import json
from datetime import datetime

class CurrencyConverter:
    """货币转换器类"""
    
    def __init__(self):
        self.base_url = "https://api.frankfurter.app"
        self.supported_currencies = ["USD", "EUR", "JPY", "GBP", "CNY", "KRW"]
    
    def get_current_rate(self, from_currency, to_currency):
        """
        获取当前汇率
        
        参数:
            from_currency (str): 源货币代码
            to_currency (str): 目标货币代码
            
        返回:
            float: 汇率值，失败时返回None
            
        TODO: 实现以下功能
        1. 构建API URL
        2. 发送请求并处理响应
        3. 从JSON数据中提取汇率
        4. 添加适当的错误处理
        """
        # 提示：URL格式为 f"{self.base_url}/latest?from={from_currency}&to={to_currency}"
        pass
    
    def get_historical_rates(self, base_currency, target_currency, days=7):
        """
        获取历史汇率数据
        
        参数:
            base_currency (str): 基础货币
            target_currency (str): 目标货币
            days (int): 获取最近几天的数据
            
        返回:
            dict: 包含日期和汇率的字典，格式为 {"日期": 汇率}
            
        TODO: 实现以下功能
        1. 计算开始日期（当前日期往前推days天）
        2. 构建历史数据API URL
        3. 处理返回的历史数据
        4. 将数据整理成易于使用的格式
        """
        # 提示：历史数据API格式为 f"{self.base_url}/2025-06-01..?base={base_currency}&symbols={target_currency}"
        # 你需要动态计算开始日期
        pass
    
    def convert_amount(self, amount, from_currency, to_currency):
        """
        转换货币金额
        
        参数:
            amount (float): 要转换的金额
            from_currency (str): 源货币
            to_currency (str): 目标货币
            
        返回:
            float: 转换后的金额
            
        TODO: 实现以下功能
        1. 获取当前汇率
        2. 计算转换后的金额
        3. 处理转换失败的情况
        """
        pass
    
    def validate_currency(self, currency_code):
        """
        验证货币代码是否有效
        
        参数:
            currency_code (str): 货币代码
            
        返回:
            bool: 是否有效
            
        TODO: 检查货币代码是否在支持的货币列表中
        """
        pass

def interactive_converter():
    """
    交互式货币转换器
    
    TODO: 实现用户交互界面
    1. 显示支持的货币列表
    2. 获取用户输入（金额、源货币、目标货币）
    3. 执行转换并显示结果
    4. 提供继续或退出的选项
    """
    converter = CurrencyConverter()
    
    print("💱 欢迎使用货币转换器！")
    print("支持的货币：", ", ".join(converter.supported_currencies))
    
    # TODO: 实现交互逻辑
    pass

def main():
    """
    主函数 - 演示所有功能
    
    TODO: 创建完整的演示程序
    1. 展示单次汇率查询
    2. 展示历史汇率获取
    3. 展示货币转换
    4. 启动交互式转换器
    """
    print("🎯 货币转换器演示程序")
    print("=" * 50)
    
    # TODO: 实现演示逻辑
    pass

if __name__ == "__main__":
    main()

"""
💡 实现提示：

1. get_current_rate() 函数：
   - 使用 requests.get() 发送请求
   - 检查 response.status_code == 200
   - 使用 response.json() 获取数据
   - 从 data["rates"][to_currency] 获取汇率

2. get_historical_rates() 函数：
   - 使用 datetime 模块计算日期
   - 构建类似 "2025-06-18.." 的日期范围
   - 处理返回的 rates 字典

3. convert_amount() 函数：
   - 调用 get_current_rate() 获取汇率
   - 计算 amount * rate

4. validate_currency() 函数：
   - 检查 currency_code.upper() in self.supported_currencies

5. interactive_converter() 函数：
   - 使用 input() 获取用户输入
   - 使用 while 循环实现重复操作
   - 添加输入验证

🎯 预期功能：
- 能够查询实时汇率
- 能够获取历史汇率数据
- 能够进行货币金额转换
- 提供友好的用户交互界面
- 具备基本的错误处理能力
"""
