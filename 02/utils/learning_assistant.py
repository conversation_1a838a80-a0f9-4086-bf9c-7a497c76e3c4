"""
学习助手 - 提供个性化学习指导和帮助

功能：
1. 概念解释和知识点梳理
2. 常见错误诊断和解决方案
3. 学习进度跟踪
4. 个性化练习推荐
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional

class ConceptExplainer:
    """概念解释器"""
    
    def __init__(self):
        self.concepts = {
            'api': {
                'name': 'API (应用程序接口)',
                'simple_explanation': 'API就像餐厅的菜单，告诉你可以点什么菜（调用什么功能）',
                'detailed_explanation': 'API是不同软件之间交流的桥梁，让我们可以获取其他服务的数据',
                'examples': [
                    '汇率API：获取最新的货币汇率',
                    '天气API：获取天气预报信息',
                    '地图API：获取地理位置信息'
                ],
                'key_points': [
                    'API需要特定的URL地址',
                    '通常返回JSON格式的数据',
                    '需要处理网络请求可能失败的情况'
                ]
            },
            'json': {
                'name': 'JSON (JavaScript对象表示法)',
                'simple_explanation': 'JSON像一个有标签的盒子，每个标签对应一个值',
                'detailed_explanation': 'JSON是一种轻量级的数据交换格式，易于人阅读和编写',
                'examples': [
                    '{"name": "张三", "age": 12}',
                    '{"rates": {"CNY": 6.5, "JPY": 110}}',
                    '{"data": [1, 2, 3, 4, 5]}'
                ],
                'key_points': [
                    '使用大括号{}表示对象',
                    '使用方括号[]表示数组',
                    '键值对用冒号:分隔',
                    '多个项目用逗号,分隔'
                ]
            },
            'matplotlib': {
                'name': 'Matplotlib (Python绘图库)',
                'simple_explanation': 'Matplotlib就像画笔和画布，帮助我们把数据变成图表',
                'detailed_explanation': 'Matplotlib是Python中最常用的绘图库，可以创建各种类型的图表',
                'examples': [
                    'plt.plot() - 绘制线图',
                    'plt.bar() - 绘制柱状图',
                    'plt.pie() - 绘制饼图'
                ],
                'key_points': [
                    '需要先导入：import matplotlib.pyplot as plt',
                    '设置标题：plt.title()',
                    '设置坐标轴：plt.xlabel(), plt.ylabel()',
                    '显示图表：plt.show()'
                ]
            },
            'requests': {
                'name': 'Requests (HTTP请求库)',
                'simple_explanation': 'Requests就像邮递员，帮我们从网上取回信息',
                'detailed_explanation': 'Requests库让Python程序能够发送HTTP请求，获取网络数据',
                'examples': [
                    'requests.get(url) - 获取网页数据',
                    'response.json() - 解析JSON数据',
                    'response.status_code - 检查请求状态'
                ],
                'key_points': [
                    '需要先安装：pip install requests',
                    '总是检查response.status_code',
                    '使用try-except处理网络错误',
                    '设置合理的timeout时间'
                ]
            }
        }
    
    def explain_concept(self, concept_key: str, level: str = 'simple') -> str:
        """解释概念"""
        if concept_key not in self.concepts:
            return f"❌ 概念 '{concept_key}' 不存在"
        
        concept = self.concepts[concept_key]
        
        if level == 'simple':
            return f"""
🎯 {concept['name']}

💡 简单理解：
{concept['simple_explanation']}

📝 举例：
{chr(10).join(f'  • {example}' for example in concept['examples'][:2])}
"""
        else:
            return f"""
🎯 {concept['name']}

💡 简单理解：
{concept['simple_explanation']}

📚 详细说明：
{concept['detailed_explanation']}

📝 示例：
{chr(10).join(f'  • {example}' for example in concept['examples'])}

🔑 关键要点：
{chr(10).join(f'  • {point}' for point in concept['key_points'])}
"""
    
    def list_available_concepts(self) -> List[str]:
        """列出可用的概念"""
        return list(self.concepts.keys())

class ErrorDiagnostic:
    """错误诊断器"""
    
    def __init__(self):
        self.common_errors = {
            'import_error': {
                'symptoms': ['ModuleNotFoundError', 'ImportError', 'No module named'],
                'diagnosis': '缺少必要的Python库',
                'solutions': [
                    '检查是否安装了所需的库',
                    '使用 pip install 命令安装缺少的库',
                    '确认Python环境配置正确'
                ],
                'examples': [
                    'pip install requests',
                    'pip install matplotlib'
                ]
            },
            'network_error': {
                'symptoms': ['ConnectionError', 'Timeout', 'requests.exceptions'],
                'diagnosis': '网络连接问题',
                'solutions': [
                    '检查网络连接是否正常',
                    '尝试增加timeout时间',
                    '检查API地址是否正确',
                    '考虑使用模拟数据进行测试'
                ],
                'examples': [
                    'requests.get(url, timeout=30)',
                    '使用try-except捕获网络异常'
                ]
            },
            'json_error': {
                'symptoms': ['JSONDecodeError', 'json.decoder', 'Expecting value'],
                'diagnosis': 'JSON数据解析失败',
                'solutions': [
                    '检查API返回的数据格式',
                    '确认response.status_code为200',
                    '打印response.text查看原始数据',
                    '检查API地址和参数是否正确'
                ],
                'examples': [
                    'print(response.text)',
                    'if response.status_code == 200: data = response.json()'
                ]
            },
            'key_error': {
                'symptoms': ['KeyError', 'key not found', 'dict key'],
                'diagnosis': '字典中不存在指定的键',
                'solutions': [
                    '检查字典的实际结构',
                    '使用dict.get()方法安全获取值',
                    '先检查键是否存在再访问',
                    '打印字典内容确认结构'
                ],
                'examples': [
                    'value = data.get("key", default_value)',
                    'if "key" in data: value = data["key"]'
                ]
            },
            'matplotlib_error': {
                'symptoms': ['matplotlib', 'plt', 'figure', 'display'],
                'diagnosis': 'Matplotlib显示或配置问题',
                'solutions': [
                    '确保安装了matplotlib库',
                    '检查是否在支持图形显示的环境中运行',
                    '尝试使用plt.savefig()保存图片',
                    '检查数据格式是否正确'
                ],
                'examples': [
                    'plt.savefig("chart.png")',
                    'plt.show() # 确保调用show()显示图表'
                ]
            }
        }
    
    def diagnose_error(self, error_message: str) -> Dict:
        """诊断错误"""
        error_message_lower = error_message.lower()
        
        for error_type, info in self.common_errors.items():
            for symptom in info['symptoms']:
                if symptom.lower() in error_message_lower:
                    return {
                        'error_type': error_type,
                        'diagnosis': info['diagnosis'],
                        'solutions': info['solutions'],
                        'examples': info['examples']
                    }
        
        return {
            'error_type': 'unknown',
            'diagnosis': '未知错误类型',
            'solutions': [
                '仔细阅读错误信息',
                '检查代码语法是否正确',
                '尝试搜索具体的错误信息',
                '寻求老师或同学的帮助'
            ],
            'examples': []
        }
    
    def format_diagnosis(self, diagnosis: Dict) -> str:
        """格式化诊断结果"""
        result = f"""
🔍 错误诊断结果

🎯 问题类型：{diagnosis['diagnosis']}

💡 解决方案：
{chr(10).join(f'  {i+1}. {solution}' for i, solution in enumerate(diagnosis['solutions']))}
"""
        
        if diagnosis['examples']:
            result += f"""
📝 示例代码：
{chr(10).join(f'  • {example}' for example in diagnosis['examples'])}
"""
        
        return result

class LearningTracker:
    """学习进度跟踪器"""
    
    def __init__(self, student_name: str):
        self.student_name = student_name
        self.progress_file = f"learning_progress_{student_name}.json"
        self.progress_data = self._load_progress()
    
    def _load_progress(self) -> Dict:
        """加载学习进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            'student_name': self.student_name,
            'start_date': datetime.now().isoformat(),
            'completed_lessons': [],
            'scores': {},
            'time_spent': {},
            'concepts_learned': [],
            'errors_encountered': []
        }
    
    def _save_progress(self):
        """保存学习进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
    
    def mark_lesson_complete(self, lesson_name: str, score: Optional[float] = None):
        """标记课程完成"""
        if lesson_name not in self.progress_data['completed_lessons']:
            self.progress_data['completed_lessons'].append(lesson_name)
        
        if score is not None:
            self.progress_data['scores'][lesson_name] = score
        
        self.progress_data['last_updated'] = datetime.now().isoformat()
        self._save_progress()
    
    def add_concept_learned(self, concept: str):
        """添加已学概念"""
        if concept not in self.progress_data['concepts_learned']:
            self.progress_data['concepts_learned'].append(concept)
            self._save_progress()
    
    def record_error(self, error_type: str, lesson: str):
        """记录遇到的错误"""
        error_record = {
            'error_type': error_type,
            'lesson': lesson,
            'timestamp': datetime.now().isoformat()
        }
        self.progress_data['errors_encountered'].append(error_record)
        self._save_progress()
    
    def get_progress_summary(self) -> str:
        """获取学习进度摘要"""
        completed = len(self.progress_data['completed_lessons'])
        avg_score = 0
        if self.progress_data['scores']:
            avg_score = sum(self.progress_data['scores'].values()) / len(self.progress_data['scores'])
        
        concepts_count = len(self.progress_data['concepts_learned'])
        
        return f"""
📊 {self.student_name} 的学习进度

✅ 已完成课程：{completed} 个
📈 平均分数：{avg_score:.1f} 分
🧠 掌握概念：{concepts_count} 个
📅 开始时间：{self.progress_data['start_date'][:10]}

📚 已完成的课程：
{chr(10).join(f'  • {lesson}' for lesson in self.progress_data['completed_lessons'])}

🎯 已掌握的概念：
{chr(10).join(f'  • {concept}' for concept in self.progress_data['concepts_learned'])}
"""

class LearningAssistant:
    """学习助手主类"""
    
    def __init__(self, student_name: str = "学生"):
        self.explainer = ConceptExplainer()
        self.diagnostic = ErrorDiagnostic()
        self.tracker = LearningTracker(student_name)
    
    def help_with_concept(self, concept: str, level: str = 'simple'):
        """帮助理解概念"""
        explanation = self.explainer.explain_concept(concept, level)
        self.tracker.add_concept_learned(concept)
        return explanation
    
    def help_with_error(self, error_message: str, lesson: str = "unknown"):
        """帮助解决错误"""
        diagnosis = self.diagnostic.diagnose_error(error_message)
        self.tracker.record_error(diagnosis['error_type'], lesson)
        return self.diagnostic.format_diagnosis(diagnosis)
    
    def show_progress(self):
        """显示学习进度"""
        return self.tracker.get_progress_summary()
    
    def interactive_help(self):
        """交互式帮助"""
        print("🤖 学习助手已启动！")
        print("我可以帮助你：")
        print("1. 解释编程概念")
        print("2. 诊断和解决错误")
        print("3. 查看学习进度")
        print("4. 退出助手")
        
        while True:
            try:
                choice = input("\n请选择功能 (1-4): ").strip()
                
                if choice == '1':
                    self._help_with_concepts()
                elif choice == '2':
                    self._help_with_errors()
                elif choice == '3':
                    print(self.show_progress())
                elif choice == '4':
                    print("👋 再见！继续加油学习！")
                    break
                else:
                    print("❌ 请输入1-4之间的数字")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出助手")
                break
    
    def _help_with_concepts(self):
        """概念帮助交互"""
        concepts = self.explainer.list_available_concepts()
        print(f"\n可用概念：{', '.join(concepts)}")
        
        concept = input("请输入要了解的概念: ").strip().lower()
        if concept in concepts:
            level = input("详细程度 (simple/detailed，默认simple): ").strip() or 'simple'
            explanation = self.help_with_concept(concept, level)
            print(explanation)
        else:
            print("❌ 概念不存在，请从可用列表中选择")
    
    def _help_with_errors(self):
        """错误帮助交互"""
        print("\n请粘贴你遇到的错误信息：")
        error_message = input().strip()
        
        if error_message:
            lesson = input("在哪个课程中遇到的？(可选): ").strip() or "unknown"
            diagnosis = self.help_with_error(error_message, lesson)
            print(diagnosis)
        else:
            print("❌ 错误信息不能为空")

def main():
    """主函数"""
    print("🎓 Python学习助手")
    print("=" * 40)
    
    student_name = input("请输入你的姓名: ").strip() or "学生"
    assistant = LearningAssistant(student_name)
    
    print(f"\n👋 你好，{student_name}！")
    assistant.interactive_help()

if __name__ == "__main__":
    main()
