# 🛠️ 教学辅助工具

这个目录包含了多个辅助工具，帮助教师和学生更好地进行Python编程学习。

## 工具列表

### 📊 data_generator.py - 数据生成器
**功能**: 为教学演示生成各种模拟数据
- 生成模拟汇率数据
- 创建历史价格序列
- 模拟市场波动和事件
- 生成投资组合数据

**使用方法**:
```bash
python data_generator.py
```

**主要类**:
- `CurrencyDataGenerator`: 货币数据生成器
- `MarketEventSimulator`: 市场事件模拟器
- `EducationalDatasets`: 教育数据集生成器

### 🌐 api_demo.py - API调用演示
**功能**: 详细演示如何使用汇率API
- 基础API调用演示
- 历史数据获取演示
- 错误处理演示
- 交互式API探索器

**使用方法**:
```bash
python api_demo.py
```

**特色功能**:
- 步骤化演示API调用过程
- 实时错误处理展示
- 交互式货币查询体验

### 🤖 learning_assistant.py - 学习助手
**功能**: 提供个性化学习指导和帮助
- 概念解释和知识点梳理
- 常见错误诊断和解决方案
- 学习进度跟踪
- 交互式帮助系统

**使用方法**:
```bash
python learning_assistant.py
```

**主要功能**:
- 解释API、JSON、Matplotlib等核心概念
- 诊断常见编程错误
- 跟踪个人学习进度

## 详细功能介绍

### 🎲 数据生成器功能

#### 1. 货币数据生成
```python
generator = CurrencyDataGenerator()

# 生成当前汇率
current_rates = generator.generate_current_rates('USD')

# 生成历史数据
historical = generator.generate_historical_data('USD', 'CNY', 30)

# 生成日内交易数据
intraday = generator.generate_intraday_data(6.5, 24)
```

#### 2. 投资组合模拟
```python
# 生成模拟投资组合
portfolio = generator.generate_portfolio_data(5)
```

#### 3. 市场事件模拟
```python
simulator = MarketEventSimulator()
event = simulator.simulate_market_event()
```

### 🌐 API演示功能

#### 1. 基础API调用演示
- 详细展示URL构建过程
- 演示HTTP请求发送
- 展示JSON数据解析
- 实际应用示例

#### 2. 错误处理演示
- 网络超时处理
- 无效参数处理
- HTTP错误状态处理
- JSON解析错误处理

#### 3. 交互式探索
- 用户自定义货币查询
- 实时汇率查询
- 历史数据查询
- 货币转换计算

### 🤖 学习助手功能

#### 1. 概念解释
支持的概念包括：
- **API**: 应用程序接口的概念和使用
- **JSON**: 数据格式的理解和解析
- **Matplotlib**: 绘图库的使用方法
- **Requests**: HTTP请求库的应用

#### 2. 错误诊断
能够诊断的错误类型：
- **导入错误**: 缺少Python库
- **网络错误**: 连接和超时问题
- **JSON错误**: 数据解析失败
- **键错误**: 字典访问问题
- **Matplotlib错误**: 图表显示问题

#### 3. 学习跟踪
- 记录完成的课程
- 跟踪学习分数
- 统计掌握的概念
- 分析常见错误

## 教师使用指南

### 课前准备
1. **运行数据生成器**，为课堂演示准备数据
2. **测试API演示工具**，确保网络连接正常
3. **准备学习助手**，了解学生可能遇到的问题

### 课堂使用
1. **API演示**: 使用api_demo.py进行实时演示
2. **数据展示**: 使用生成的模拟数据进行教学
3. **问题解答**: 使用学习助手帮助学生理解概念

### 课后辅导
1. **个性化指导**: 根据学习助手的进度跟踪提供建议
2. **错误分析**: 分析学生常见错误，调整教学重点
3. **进度监控**: 跟踪学生学习进度，及时提供帮助

## 学生使用指南

### 自主学习
1. **概念学习**: 使用学习助手了解不熟悉的概念
2. **API练习**: 通过API演示工具练习网络请求
3. **数据实验**: 使用数据生成器获取练习数据

### 问题解决
1. **错误诊断**: 遇到错误时使用学习助手诊断
2. **概念复习**: 不理解的概念可以随时查询
3. **进度跟踪**: 查看自己的学习进度和成果

### 实践练习
1. **修改参数**: 尝试修改数据生成器的参数
2. **探索API**: 使用不同的货币组合测试API
3. **自定义功能**: 在理解基础功能后尝试添加新功能

## 扩展和定制

### 添加新概念
在`learning_assistant.py`中的`concepts`字典中添加新概念：
```python
'new_concept': {
    'name': '新概念名称',
    'simple_explanation': '简单解释',
    'detailed_explanation': '详细解释',
    'examples': ['示例1', '示例2'],
    'key_points': ['要点1', '要点2']
}
```

### 添加新错误类型
在`common_errors`字典中添加新的错误诊断：
```python
'new_error': {
    'symptoms': ['错误关键词1', '错误关键词2'],
    'diagnosis': '错误诊断',
    'solutions': ['解决方案1', '解决方案2'],
    'examples': ['示例代码1', '示例代码2']
}
```

### 自定义数据生成
修改`CurrencyDataGenerator`类中的参数：
```python
# 修改支持的货币
self.currencies = {
    'USD': {'name': '美元', 'base_rate': 1.0},
    # 添加更多货币...
}

# 调整波动率
volatility = random.uniform(-0.1, 0.1)  # 调整为±10%
```

## 技术要求

### 依赖库
```bash
pip install requests numpy matplotlib
```

### Python版本
- Python 3.6+

### 网络要求
- API演示工具需要网络连接
- 其他工具可离线使用

## 常见问题

### Q: 数据生成器生成的数据是真实的吗？
A: 不是，这些都是基于真实汇率模式的模拟数据，用于教学演示。

### Q: API演示工具无法连接怎么办？
A: 检查网络连接，或者使用数据生成器提供的模拟数据进行练习。

### Q: 学习助手的进度文件在哪里？
A: 在当前目录下，文件名为`learning_progress_[学生姓名].json`。

### Q: 如何重置学习进度？
A: 删除对应的进度文件，重新运行学习助手即可。

## 更新日志

### v1.0.0
- 初始版本发布
- 包含数据生成器、API演示和学习助手
- 支持基础的概念解释和错误诊断

这些工具旨在让Python编程学习变得更加有趣和高效！
