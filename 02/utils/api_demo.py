"""
API调用演示工具 - 展示如何使用汇率API

功能：
1. 演示API调用过程
2. 展示JSON数据解析
3. 处理网络错误情况
4. 提供交互式学习体验
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Optional, List

class APIDemo:
    """API演示类"""
    
    def __init__(self):
        self.base_url = "https://api.frankfurter.app"
        self.demo_mode = True  # 演示模式，会显示详细步骤
    
    def demonstrate_basic_api_call(self):
        """演示基础API调用"""
        print("🌟 基础API调用演示")
        print("=" * 40)
        
        # 步骤1：构建URL
        from_currency = "JPY"
        to_currency = "CNY"
        url = f"{self.base_url}/latest?from={from_currency}&to={to_currency}"
        
        print(f"📡 步骤1：构建API请求URL")
        print(f"   URL: {url}")
        print(f"   说明: 获取{from_currency}到{to_currency}的最新汇率")
        
        # 步骤2：发送请求
        print(f"\n🚀 步骤2：发送HTTP请求")
        try:
            print("   正在连接API服务器...")
            response = requests.get(url, timeout=10)
            print(f"   ✅ 请求成功！状态码: {response.status_code}")
            
            # 步骤3：检查响应状态
            print(f"\n🔍 步骤3：检查响应状态")
            if response.status_code == 200:
                print("   ✅ 状态码200 - 请求成功")
            else:
                print(f"   ❌ 状态码{response.status_code} - 请求失败")
                return
            
            # 步骤4：解析JSON数据
            print(f"\n📊 步骤4：解析JSON响应数据")
            data = response.json()
            print("   原始JSON数据:")
            print(f"   {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 步骤5：提取汇率信息
            print(f"\n💰 步骤5：提取汇率信息")
            if 'rates' in data and to_currency in data['rates']:
                rate = data['rates'][to_currency]
                print(f"   汇率提取成功: 1 {from_currency} = {rate} {to_currency}")
                
                # 步骤6：实际应用
                print(f"\n🧮 步骤6：实际货币转换应用")
                amounts = [100, 500, 1000]
                for amount in amounts:
                    converted = amount * rate
                    print(f"   {amount} {from_currency} = {converted:.2f} {to_currency}")
            else:
                print("   ❌ 无法找到汇率数据")
                
        except requests.exceptions.Timeout:
            print("   ⏰ 请求超时 - 网络连接可能较慢")
        except requests.exceptions.ConnectionError:
            print("   🌐 连接错误 - 请检查网络连接")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求异常: {e}")
        except json.JSONDecodeError:
            print("   📄 JSON解析错误 - 响应格式不正确")
    
    def demonstrate_historical_api(self):
        """演示历史数据API调用"""
        print("\n📈 历史数据API调用演示")
        print("=" * 40)
        
        # 构建历史数据URL
        base_currency = "CNY"
        target_currency = "JPY"
        start_date = "2025-06-01"
        url = f"{self.base_url}/{start_date}..?base={base_currency}&symbols={target_currency}"
        
        print(f"📡 历史数据API URL:")
        print(f"   {url}")
        print(f"   说明: 获取从{start_date}到现在的{base_currency}到{target_currency}汇率")
        
        try:
            print(f"\n🚀 发送历史数据请求...")
            response = requests.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 历史数据获取成功！")
                
                # 显示数据结构
                print(f"\n📊 数据结构分析:")
                print(f"   基础货币: {data.get('base', 'N/A')}")
                print(f"   开始日期: {data.get('start_date', 'N/A')}")
                print(f"   结束日期: {data.get('end_date', 'N/A')}")
                
                # 显示部分历史数据
                rates_data = data.get('rates', {})
                print(f"\n📅 历史汇率数据（显示最近5天）:")
                
                # 获取最近5天的数据
                sorted_dates = sorted(rates_data.keys())[-5:]
                for date in sorted_dates:
                    rate = rates_data[date].get(target_currency, 'N/A')
                    print(f"   {date}: 1 {base_currency} = {rate} {target_currency}")
                
                # 简单趋势分析
                if len(sorted_dates) >= 2:
                    first_rate = rates_data[sorted_dates[0]].get(target_currency, 0)
                    last_rate = rates_data[sorted_dates[-1]].get(target_currency, 0)
                    change = ((last_rate - first_rate) / first_rate) * 100
                    trend = "📈 上升" if change > 0 else "📉 下降"
                    print(f"\n📊 趋势分析: {trend} ({change:+.2f}%)")
                    
            else:
                print(f"❌ 历史数据获取失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 历史数据请求异常: {e}")
    
    def demonstrate_error_handling(self):
        """演示错误处理"""
        print("\n🚨 错误处理演示")
        print("=" * 40)
        
        error_scenarios = [
            {
                'name': '无效货币代码',
                'url': f"{self.base_url}/latest?from=INVALID&to=CNY",
                'expected': '400或404错误'
            },
            {
                'name': '错误的API端点',
                'url': f"{self.base_url}/wrong_endpoint",
                'expected': '404错误'
            },
            {
                'name': '网络超时模拟',
                'url': f"{self.base_url}/latest?from=USD&to=CNY",
                'timeout': 0.001,  # 极短超时时间
                'expected': '超时异常'
            }
        ]
        
        for i, scenario in enumerate(error_scenarios, 1):
            print(f"\n🧪 错误场景 {i}: {scenario['name']}")
            print(f"   URL: {scenario['url']}")
            print(f"   预期结果: {scenario['expected']}")
            
            try:
                timeout = scenario.get('timeout', 10)
                response = requests.get(scenario['url'], timeout=timeout)
                
                if response.status_code == 200:
                    print("   ✅ 请求成功（意外结果）")
                else:
                    print(f"   ⚠️ HTTP错误 {response.status_code}: {response.reason}")
                    
            except requests.exceptions.Timeout:
                print("   ⏰ 超时异常 - 这是预期的结果")
            except requests.exceptions.ConnectionError:
                print("   🌐 连接异常 - 网络问题")
            except requests.exceptions.HTTPError as e:
                print(f"   🚫 HTTP异常: {e}")
            except Exception as e:
                print(f"   ❌ 其他异常: {e}")
    
    def interactive_api_explorer(self):
        """交互式API探索器"""
        print("\n🎮 交互式API探索器")
        print("=" * 40)
        print("你可以自己尝试不同的货币组合！")
        
        available_currencies = ['USD', 'EUR', 'JPY', 'GBP', 'CNY', 'KRW', 'AUD', 'CAD']
        print(f"可用货币: {', '.join(available_currencies)}")
        
        while True:
            print(f"\n请选择操作:")
            print("1. 查询实时汇率")
            print("2. 查询历史汇率")
            print("3. 退出探索器")
            
            try:
                choice = input("请输入选择 (1-3): ").strip()
                
                if choice == '1':
                    self._interactive_current_rate()
                elif choice == '2':
                    self._interactive_historical_rate()
                elif choice == '3':
                    print("👋 感谢使用API探索器！")
                    break
                else:
                    print("❌ 无效选择，请输入1-3")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出探索器")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
    
    def _interactive_current_rate(self):
        """交互式当前汇率查询"""
        try:
            from_curr = input("请输入源货币代码 (如 USD): ").strip().upper()
            to_curr = input("请输入目标货币代码 (如 CNY): ").strip().upper()
            
            if not from_curr or not to_curr:
                print("❌ 货币代码不能为空")
                return
            
            print(f"\n🔍 查询 {from_curr} 到 {to_curr} 的汇率...")
            url = f"{self.base_url}/latest?from={from_curr}&to={to_curr}"
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'rates' in data and to_curr in data['rates']:
                    rate = data['rates'][to_curr]
                    print(f"✅ 汇率查询成功: 1 {from_curr} = {rate} {to_curr}")
                    
                    # 询问是否进行转换计算
                    amount_str = input(f"请输入要转换的{from_curr}金额 (直接回车跳过): ").strip()
                    if amount_str:
                        try:
                            amount = float(amount_str)
                            converted = amount * rate
                            print(f"💰 转换结果: {amount} {from_curr} = {converted:.2f} {to_curr}")
                        except ValueError:
                            print("❌ 金额格式不正确")
                else:
                    print("❌ 无法获取汇率数据，请检查货币代码")
            else:
                print(f"❌ 查询失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 查询过程中发生错误: {e}")
    
    def _interactive_historical_rate(self):
        """交互式历史汇率查询"""
        try:
            base_curr = input("请输入基础货币代码 (如 USD): ").strip().upper()
            target_curr = input("请输入目标货币代码 (如 CNY): ").strip().upper()
            start_date = input("请输入开始日期 (YYYY-MM-DD，如 2025-06-01): ").strip()
            
            if not all([base_curr, target_curr, start_date]):
                print("❌ 所有字段都不能为空")
                return
            
            print(f"\n🔍 查询 {base_curr} 到 {target_curr} 从 {start_date} 的历史汇率...")
            url = f"{self.base_url}/{start_date}..?base={base_curr}&symbols={target_curr}"
            
            response = requests.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                rates_data = data.get('rates', {})
                
                if rates_data:
                    print(f"✅ 历史数据获取成功，共 {len(rates_data)} 天的数据")
                    
                    # 显示最近几天的数据
                    sorted_dates = sorted(rates_data.keys())[-5:]
                    print(f"\n📅 最近5天的汇率:")
                    for date in sorted_dates:
                        rate = rates_data[date].get(target_curr, 'N/A')
                        print(f"   {date}: {rate}")
                else:
                    print("❌ 没有找到历史数据")
            else:
                print(f"❌ 查询失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 查询过程中发生错误: {e}")

def main():
    """主函数 - 运行API演示"""
    print("🎯 汇率API调用演示工具")
    print("=" * 50)
    print("这个工具将帮助你理解如何使用汇率API")
    print("包括基础调用、错误处理和交互式探索")
    
    demo = APIDemo()
    
    # 基础演示
    demo.demonstrate_basic_api_call()
    
    # 历史数据演示
    demo.demonstrate_historical_api()
    
    # 错误处理演示
    demo.demonstrate_error_handling()
    
    # 询问是否进入交互模式
    print("\n" + "=" * 50)
    choice = input("是否进入交互式API探索器？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        demo.interactive_api_explorer()
    
    print("\n🎉 API演示完成！")
    print("💡 现在你应该了解了:")
    print("   - 如何构建API请求URL")
    print("   - 如何发送HTTP请求")
    print("   - 如何解析JSON响应")
    print("   - 如何处理各种错误情况")
    print("   - 如何将API数据用于实际应用")

if __name__ == "__main__":
    main()
