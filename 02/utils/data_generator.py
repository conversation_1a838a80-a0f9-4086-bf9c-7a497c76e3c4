"""
数据生成器 - 为教学演示生成模拟数据

功能：
1. 生成模拟汇率数据
2. 创建历史价格序列
3. 模拟市场波动
4. 生成投资组合数据
"""

import random
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import numpy as np

class CurrencyDataGenerator:
    """货币数据生成器"""
    
    def __init__(self):
        self.currencies = {
            'USD': {'name': '美元', 'base_rate': 1.0},
            'EUR': {'name': '欧元', 'base_rate': 0.85},
            'JPY': {'name': '日元', 'base_rate': 110.0},
            'GBP': {'name': '英镑', 'base_rate': 0.75},
            'CNY': {'name': '人民币', 'base_rate': 6.5},
            'KRW': {'name': '韩元', 'base_rate': 1200.0},
            'AUD': {'name': '澳元', 'base_rate': 1.35},
            'CAD': {'name': '加元', 'base_rate': 1.25}
        }
    
    def generate_current_rates(self, base_currency: str = 'USD') -> Dict[str, float]:
        """生成当前汇率数据"""
        rates = {}
        base_rate = self.currencies[base_currency]['base_rate']
        
        for currency, info in self.currencies.items():
            if currency != base_currency:
                # 基础汇率 + 随机波动
                base_value = info['base_rate'] / base_rate
                volatility = random.uniform(-0.05, 0.05)  # ±5%波动
                rates[currency] = round(base_value * (1 + volatility), 4)
        
        return rates
    
    def generate_historical_data(self, base_currency: str, target_currency: str, 
                               days: int = 30) -> Dict[str, float]:
        """生成历史汇率数据"""
        historical_data = {}
        
        # 获取基础汇率
        base_rate = self.currencies[base_currency]['base_rate']
        target_rate = self.currencies[target_currency]['base_rate']
        initial_rate = target_rate / base_rate
        
        current_rate = initial_rate
        
        for i in range(days):
            date = (datetime.now() - timedelta(days=days-i-1)).strftime('%Y-%m-%d')
            
            # 模拟随机游走
            daily_change = random.gauss(0, 0.02)  # 正态分布，标准差2%
            current_rate *= (1 + daily_change)
            
            historical_data[date] = round(current_rate, 4)
        
        return historical_data
    
    def generate_intraday_data(self, base_rate: float, hours: int = 24) -> List[Dict]:
        """生成日内交易数据"""
        data = []
        current_rate = base_rate
        
        for hour in range(hours):
            # 模拟每小时的价格变化
            change = random.gauss(0, 0.005)  # 小幅波动
            current_rate *= (1 + change)
            
            # 生成OHLC数据
            open_price = current_rate
            high_price = open_price * (1 + abs(random.gauss(0, 0.003)))
            low_price = open_price * (1 - abs(random.gauss(0, 0.003)))
            close_price = open_price + random.gauss(0, 0.002)
            
            data.append({
                'time': f"{hour:02d}:00",
                'open': round(open_price, 4),
                'high': round(high_price, 4),
                'low': round(low_price, 4),
                'close': round(close_price, 4),
                'volume': random.randint(1000, 10000)
            })
            
            current_rate = close_price
        
        return data
    
    def generate_portfolio_data(self, num_currencies: int = 5) -> Dict[str, Dict]:
        """生成投资组合数据"""
        selected_currencies = random.sample(list(self.currencies.keys()), num_currencies)
        portfolio = {}
        
        total_value = 100000  # 总投资额
        remaining_value = total_value
        
        for i, currency in enumerate(selected_currencies):
            if i == len(selected_currencies) - 1:
                # 最后一个货币分配剩余金额
                amount = remaining_value
            else:
                # 随机分配10-40%的资金
                percentage = random.uniform(0.1, 0.4)
                amount = total_value * percentage
                remaining_value -= amount
            
            # 生成购买信息
            purchase_rate = self.currencies[currency]['base_rate'] * random.uniform(0.95, 1.05)
            current_rate = self.currencies[currency]['base_rate']
            
            portfolio[currency] = {
                'name': self.currencies[currency]['name'],
                'amount': round(amount, 2),
                'purchase_rate': round(purchase_rate, 4),
                'current_rate': round(current_rate, 4),
                'units': round(amount / purchase_rate, 2),
                'current_value': round((amount / purchase_rate) * current_rate, 2),
                'profit_loss': round(((amount / purchase_rate) * current_rate) - amount, 2)
            }
        
        return portfolio

class MarketEventSimulator:
    """市场事件模拟器"""
    
    def __init__(self):
        self.event_types = [
            {'name': '央行降息', 'impact': -0.03, 'probability': 0.1},
            {'name': '经济数据利好', 'impact': 0.02, 'probability': 0.15},
            {'name': '政治不稳定', 'impact': -0.05, 'probability': 0.05},
            {'name': '贸易协议签署', 'impact': 0.04, 'probability': 0.08},
            {'name': '通胀数据公布', 'impact': 0.01, 'probability': 0.2}
        ]
    
    def simulate_market_event(self) -> Dict:
        """模拟市场事件"""
        if random.random() < 0.3:  # 30%概率发生事件
            event = random.choice(self.event_types)
            return {
                'occurred': True,
                'event_name': event['name'],
                'impact': event['impact'],
                'description': f"市场事件：{event['name']}，预期影响：{event['impact']*100:+.1f}%"
            }
        else:
            return {'occurred': False}
    
    def apply_event_impact(self, base_data: List[float], event: Dict) -> List[float]:
        """将事件影响应用到数据中"""
        if not event['occurred']:
            return base_data
        
        impact = event['impact']
        affected_data = []
        
        for i, value in enumerate(base_data):
            if i >= len(base_data) * 0.7:  # 事件影响后30%的数据
                affected_data.append(value * (1 + impact))
            else:
                affected_data.append(value)
        
        return affected_data

class EducationalDatasets:
    """教育数据集生成器"""
    
    def __init__(self):
        self.generator = CurrencyDataGenerator()
        self.simulator = MarketEventSimulator()
    
    def create_beginner_dataset(self) -> Dict:
        """创建初学者数据集"""
        return {
            'simple_rates': {
                'JPY_to_CNY': 0.049,
                'USD_to_CNY': 6.5,
                'EUR_to_CNY': 7.8
            },
            'conversion_examples': [
                {'amount': 100, 'from': 'JPY', 'to': 'CNY', 'result': 4.9},
                {'amount': 500, 'from': 'JPY', 'to': 'CNY', 'result': 24.5},
                {'amount': 1000, 'from': 'JPY', 'to': 'CNY', 'result': 49.0}
            ],
            'chart_data': {
                'dates': ['6-19', '6-20', '6-21', '6-22', '6-23', '6-24', '6-25'],
                'rates': [0.048, 0.049, 0.047, 0.050, 0.049, 0.048, 0.049]
            }
        }
    
    def create_intermediate_dataset(self) -> Dict:
        """创建中级数据集"""
        return {
            'multiple_currencies': self.generator.generate_current_rates('USD'),
            'historical_data': {
                'USD_CNY': self.generator.generate_historical_data('USD', 'CNY', 14),
                'EUR_JPY': self.generator.generate_historical_data('EUR', 'JPY', 14)
            },
            'portfolio_example': self.generator.generate_portfolio_data(4),
            'market_scenarios': [
                self.simulator.simulate_market_event() for _ in range(5)
            ]
        }
    
    def create_advanced_dataset(self) -> Dict:
        """创建高级数据集"""
        return {
            'real_time_simulation': self.generator.generate_intraday_data(6.5, 48),
            'multi_currency_portfolio': self.generator.generate_portfolio_data(8),
            'correlation_data': self._generate_correlation_matrix(),
            'volatility_analysis': self._generate_volatility_data(),
            'technical_indicators': self._generate_technical_data()
        }
    
    def _generate_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """生成货币相关性矩阵"""
        currencies = ['USD', 'EUR', 'JPY', 'GBP', 'CNY']
        matrix = {}
        
        for curr1 in currencies:
            matrix[curr1] = {}
            for curr2 in currencies:
                if curr1 == curr2:
                    matrix[curr1][curr2] = 1.0
                else:
                    # 生成-1到1之间的相关系数
                    correlation = random.uniform(-0.8, 0.8)
                    matrix[curr1][curr2] = round(correlation, 3)
        
        return matrix
    
    def _generate_volatility_data(self) -> Dict[str, float]:
        """生成波动率数据"""
        currencies = ['USD', 'EUR', 'JPY', 'GBP', 'CNY', 'KRW']
        return {
            currency: round(random.uniform(0.05, 0.25), 3) 
            for currency in currencies
        }
    
    def _generate_technical_data(self) -> Dict:
        """生成技术分析数据"""
        prices = [100 + random.gauss(0, 5) for _ in range(50)]
        
        # 简单移动平均
        sma_20 = []
        for i in range(19, len(prices)):
            sma_20.append(sum(prices[i-19:i+1]) / 20)
        
        return {
            'prices': [round(p, 2) for p in prices],
            'sma_20': [round(s, 2) for s in sma_20],
            'volume': [random.randint(1000, 5000) for _ in range(50)],
            'rsi': [round(random.uniform(20, 80), 1) for _ in range(30)]
        }

def save_datasets_to_files():
    """将数据集保存到文件"""
    datasets = EducationalDatasets()
    
    # 创建各难度级别的数据集
    beginner_data = datasets.create_beginner_dataset()
    intermediate_data = datasets.create_intermediate_dataset()
    advanced_data = datasets.create_advanced_dataset()
    
    # 保存到JSON文件
    with open('beginner_dataset.json', 'w', encoding='utf-8') as f:
        json.dump(beginner_data, f, ensure_ascii=False, indent=2)
    
    with open('intermediate_dataset.json', 'w', encoding='utf-8') as f:
        json.dump(intermediate_data, f, ensure_ascii=False, indent=2)
    
    with open('advanced_dataset.json', 'w', encoding='utf-8') as f:
        json.dump(advanced_data, f, ensure_ascii=False, indent=2)
    
    print("✅ 数据集文件已生成：")
    print("📁 beginner_dataset.json - 初学者数据集")
    print("📁 intermediate_dataset.json - 中级数据集") 
    print("📁 advanced_dataset.json - 高级数据集")

def main():
    """主函数 - 演示数据生成功能"""
    print("🎲 教学数据生成器演示")
    print("=" * 50)
    
    generator = CurrencyDataGenerator()
    
    # 演示当前汇率生成
    print("💱 当前汇率数据：")
    current_rates = generator.generate_current_rates('USD')
    for currency, rate in current_rates.items():
        print(f"  USD/{currency}: {rate}")
    
    print("\n📈 历史数据示例（USD/CNY，最近7天）：")
    historical = generator.generate_historical_data('USD', 'CNY', 7)
    for date, rate in historical.items():
        print(f"  {date}: {rate}")
    
    print("\n💼 投资组合示例：")
    portfolio = generator.generate_portfolio_data(3)
    for currency, info in portfolio.items():
        profit_loss = info['profit_loss']
        status = "📈" if profit_loss > 0 else "📉"
        print(f"  {status} {info['name']}: {info['amount']}元 → {info['current_value']}元 ({profit_loss:+.2f})")
    
    # 生成完整数据集文件
    print("\n🗂️ 生成完整数据集文件...")
    save_datasets_to_files()

if __name__ == "__main__":
    main()
