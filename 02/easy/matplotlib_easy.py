"""
简单难度：Matplotlib绘图 - 填空题

学习目标：
1. 掌握plt.plot()基本绘图
2. 学会添加图表标题和坐标轴标签
3. 理解如何显示网格和图表

任务：请在标有 "# TODO: 填空" 的地方填入正确的代码
"""

import matplotlib.pyplot as plt

def create_exchange_rate_chart():
    """
    创建汇率变化图表（使用模拟数据）
    """
    print("📈 开始创建汇率图表...")
    
    # 模拟数据：最近7天的日期和汇率
    dates = ['6-19', '6-20', '6-21', '6-22', '6-23', '6-24', '6-25']
    rates = [0.048, 0.049, 0.047, 0.050, 0.049, 0.048, 0.049]
    
    print("📊 数据准备完成：")
    print("日期：", dates)
    print("汇率：", rates)
    
    # 创建图表
    print("🎨 正在绘制图表...")
    
    # TODO: 填空 - 使用plt.plot()绘制线图，x轴是dates，y轴是rates
    plt.______(dates, rates)
    
    # 设置图表标题
    # TODO: 填空 - 使用plt.title()设置标题为"日元到人民币汇率变化"
    plt.______("日元到人民币汇率变化")
    
    # 设置x轴标签
    # TODO: 填空 - 使用plt.xlabel()设置x轴标签为"日期"
    plt.______("日期")
    
    # 设置y轴标签
    # TODO: 填空 - 使用plt.ylabel()设置y轴标签为"汇率 (CNY)"
    plt.______("汇率 (CNY)")
    
    # 显示网格
    # TODO: 填空 - 使用plt.grid()显示网格，参数设为True
    plt.______(True)
    
    # 显示图表
    # TODO: 填空 - 使用plt.show()显示图表
    plt.______()
    
    print("✅ 图表显示完成！")

def create_currency_comparison():
    """
    创建不同金额的货币转换对比图
    """
    print("\n💱 创建货币转换对比图...")
    
    # 模拟数据：不同日元金额及其对应的人民币金额
    jpy_amounts = [100, 500, 1000, 2000, 5000, 10000]
    cny_amounts = [4.9, 24.5, 49.0, 98.0, 245.0, 490.0]
    
    print("📊 转换数据：")
    for jpy, cny in zip(jpy_amounts, cny_amounts):
        print(f"  {jpy}日元 → {cny}人民币")
    
    # 创建柱状图
    # TODO: 填空 - 使用plt.bar()创建柱状图
    plt.______(jpy_amounts, cny_amounts)
    
    # TODO: 填空 - 设置标题为"日元转人民币金额对比"
    plt.______("日元转人民币金额对比")
    
    # TODO: 填空 - 设置x轴标签为"日元金额"
    plt.______("日元金额")
    
    # TODO: 填空 - 设置y轴标签为"人民币金额"
    plt.______("人民币金额")
    
    # TODO: 填空 - 显示网格
    plt.______(True)
    
    # TODO: 填空 - 显示图表
    plt.______()
    
    print("✅ 对比图显示完成！")

def main():
    """
    主函数 - 运行所有图表演示
    """
    print("🎯 欢迎使用Matplotlib绘图演示！")
    print("=" * 50)
    
    # 创建汇率变化图
    create_exchange_rate_chart()
    
    print("\n" + "=" * 50)
    
    # 创建货币转换对比图
    create_currency_comparison()
    
    print("\n🎉 所有图表演示完成！")

# 运行程序
if __name__ == "__main__":
    main()

"""
💡 填空答案提示：
1. plot - 绘制线图
2. title - 设置标题
3. xlabel - 设置x轴标签
4. ylabel - 设置y轴标签
5. grid - 显示网格
6. show - 显示图表
7. bar - 绘制柱状图
8. title - 设置标题
9. xlabel - 设置x轴标签
10. ylabel - 设置y轴标签
11. grid - 显示网格
12. show - 显示图表
"""
