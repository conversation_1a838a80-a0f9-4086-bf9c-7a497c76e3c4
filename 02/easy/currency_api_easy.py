"""
简单难度：汇率API使用 - 填空题

学习目标：
1. 理解如何使用requests库获取网络数据
2. 学会从JSON数据中提取特定信息
3. 掌握基本的数据处理

任务：请在标有 "# TODO: 填空" 的地方填入正确的代码
"""

import requests
import json

def get_exchange_rate():
    """
    获取日元(JPY)到人民币(CNY)的汇率
    """
    print("🌟 开始获取汇率信息...")
    
    # API网址 - 获取日元到人民币的汇率
    url = "https://api.frankfurter.app/latest?from=JPY&to=CNY"
    
    try:
        # 发送网络请求
        print("📡 正在连接汇率API...")
        response = requests.get(url)
        
        # 检查请求是否成功
        if response.status_code == 200:
            print("✅ 成功获取数据！")
            
            # 将JSON字符串转换为Python字典
            # TODO: 填空 - 使用response.json()方法获取数据
            data = _______________
            
            print("📊 原始数据：", data)
            
            # 从数据中提取汇率信息
            # 数据结构示例: {"amount": 1.0, "base": "JPY", "date": "2025-06-25", "rates": {"CNY": 0.049}}
            
            # TODO: 填空 - 从data字典中获取rates部分
            rates = data["_______"]
            
            # TODO: 填空 - 从rates中获取CNY的汇率值
            cny_rate = rates["_____"]
            
            print(f"💰 1日元 = {cny_rate}人民币")
            return cny_rate
            
        else:
            print("❌ 获取数据失败，状态码：", response.status_code)
            return None
            
    except Exception as e:
        print("🚨 发生错误：", str(e))
        return None

def convert_currency(amount, rate):
    """
    货币转换计算
    """
    # TODO: 填空 - 计算转换后的金额（日元金额 × 汇率）
    converted_amount = amount * _____
    
    print(f"💴 {amount}日元 = {converted_amount:.2f}人民币")
    return converted_amount

def main():
    """
    主函数 - 程序入口
    """
    print("🎯 欢迎使用汇率查询小程序！")
    print("=" * 40)
    
    # 获取汇率
    rate = get_exchange_rate()
    
    if rate:
        print("\n" + "=" * 40)
        print("💱 货币转换计算器")
        
        # 示例转换
        test_amounts = [100, 500, 1000, 5000]
        
        for amount in test_amounts:
            # TODO: 填空 - 调用convert_currency函数
            convert_currency(amount, _____)
    
    print("\n🎉 程序运行完成！")

# 运行程序
if __name__ == "__main__":
    main()

"""
💡 填空答案提示：
1. response.json() - 获取JSON数据
2. "rates" - 获取汇率数据部分
3. "CNY" - 获取人民币汇率
4. rate - 使用汇率进行计算
5. rate - 传递汇率参数
"""
