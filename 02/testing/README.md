# 🧪 自动测试打分系统

## 系统简介
这是一个专为教学设计的自动化代码评估系统，能够自动测试学生代码、分析代码质量并生成详细的评分报告。

## 主要功能

### 🔍 代码分析
- **结构分析**: 统计函数、类、注释行数
- **质量评估**: 检查文档字符串覆盖率、代码复杂度
- **规范检查**: 验证编码规范和最佳实践

### 🧪 功能测试
- **函数测试**: 自动测试指定函数的输入输出
- **输出测试**: 验证程序输出是否包含期望内容
- **边界测试**: 测试异常情况和边界条件

### 📊 评分系统
- **多维度评分**: 功能实现、代码质量、输出正确性
- **权重配置**: 可根据教学重点调整评分权重
- **等级划分**: A-F等级制，便于理解

### 📄 报告生成
- **HTML报告**: 美观的可视化评分报告
- **JSON报告**: 结构化数据，便于进一步分析
- **批量报告**: 支持班级整体评估和排名

## 文件结构

```
testing/
├── auto_grader.py          # 主评分程序
├── test_configs.json       # 测试配置文件
├── README.md              # 使用说明
└── reports/               # 生成的报告目录
    ├── student_report.html
    ├── student_report.json
    └── class_summary.html
```

## 使用方法

### 1. 单个文件评估
```bash
python auto_grader.py --file <文件路径> --difficulty <难度级别>
```

**示例**:
```bash
python auto_grader.py --file ../easy/currency_api_easy.py --difficulty easy
```

### 2. 批量评估
```bash
python auto_grader.py --batch <目录路径> --difficulty <难度级别>
```

**示例**:
```bash
python auto_grader.py --batch ./submissions --difficulty medium
```

### 3. 演示模式
```bash
python auto_grader.py --demo
```

## 配置说明

### 测试配置文件 (test_configs.json)

#### 简单难度配置
```json
{
  "easy": {
    "currency_api_easy": {
      "output_tests": {
        "expected_outputs": ["汇率", "人民币", "成功获取数据"]
      },
      "code_requirements": {
        "required_imports": ["requests", "json"],
        "required_functions": ["get_exchange_rate", "convert_currency"],
        "min_functions": 3
      }
    }
  }
}
```

#### 中等难度配置
```json
{
  "medium": {
    "currency_api_medium": {
      "function_tests": [
        {
          "function_name": "validate_currency",
          "test_cases": [
            {"input": ["USD"], "expected": true},
            {"input": ["INVALID"], "expected": false}
          ],
          "weight": 0.15
        }
      ],
      "code_requirements": {
        "required_classes": ["CurrencyConverter"],
        "min_methods": 4
      }
    }
  }
}
```

## 评分标准

### 评分维度
1. **功能实现 (40%)**
   - 核心功能是否正确实现
   - 函数测试通过率
   - 边界情况处理

2. **代码质量 (30%)**
   - 代码结构和组织
   - 文档字符串覆盖率
   - 注释质量和数量
   - 编码规范遵循

3. **输出正确性 (20%)**
   - 程序输出是否符合要求
   - 用户界面友好性
   - 错误信息清晰度

4. **额外加分 (10%)**
   - 创新功能实现
   - 代码优雅性
   - 性能优化

### 等级划分
- **A (90-100分)**: 优秀 - 完全掌握所有概念
- **B (80-89分)**: 良好 - 掌握大部分概念  
- **C (70-79分)**: 中等 - 掌握基本概念
- **D (60-69分)**: 及格 - 部分掌握概念
- **F (0-59分)**: 不及格 - 需要重新学习

## 报告解读

### HTML报告内容
1. **总分显示**: 醒目的分数展示
2. **分数分解**: 各维度得分详情
3. **详细反馈**: 具体的测试结果
4. **改进建议**: 针对性的学习建议

### 班级汇总报告
- 参与人数统计
- 平均分、最高分、最低分
- 学生排名和等级分布
- 整体表现分析

## 教师使用指南

### 课前准备
1. **配置测试用例**: 根据教学目标调整test_configs.json
2. **准备评估环境**: 确保Python环境和依赖库正常
3. **创建提交目录**: 为学生作业创建统一的提交文件夹

### 评估流程
1. **收集作业**: 将学生代码放入指定目录
2. **批量评估**: 运行批量评估命令
3. **查看报告**: 检查生成的HTML和JSON报告
4. **反馈学生**: 将个人报告发送给对应学生

### 结果分析
1. **整体表现**: 查看班级汇总报告了解整体水平
2. **问题识别**: 分析常见错误和薄弱环节
3. **教学调整**: 根据评估结果调整后续教学重点

## 学生使用指南

### 自我评估
学生可以使用此工具进行自我评估：
```bash
python auto_grader.py --file my_homework.py --difficulty easy
```

### 报告理解
1. **查看总分**: 了解整体表现
2. **分析分解**: 识别强项和弱项
3. **阅读建议**: 按照改进建议优化代码
4. **重新测试**: 修改后再次评估验证改进

## 扩展功能

### 自定义测试用例
教师可以根据具体教学需求添加自定义测试：
```json
{
  "custom_tests": [
    {
      "name": "特殊功能测试",
      "function_name": "special_function",
      "test_cases": [...]
    }
  ]
}
```

### 性能测试
对于高级课程，可以添加性能测试：
```json
{
  "performance_tests": {
    "max_execution_time": 5.0,
    "memory_limit": "100MB"
  }
}
```

## 常见问题

### Q: 如何添加新的测试用例？
A: 编辑test_configs.json文件，在对应难度级别下添加测试配置

### Q: 评分权重可以调整吗？
A: 可以，在配置文件中修改scoring_weights部分

### Q: 支持哪些编程语言？
A: 目前主要支持Python，可以扩展支持其他语言

### Q: 如何处理网络依赖的代码？
A: 系统会尝试运行代码，如果网络不可用会在报告中标注

## 技术支持

如果在使用过程中遇到问题，请检查：
1. Python环境是否正确安装
2. 必要的依赖库是否已安装
3. 文件路径是否正确
4. 配置文件格式是否正确

更多技术细节请参考auto_grader.py中的代码注释。
