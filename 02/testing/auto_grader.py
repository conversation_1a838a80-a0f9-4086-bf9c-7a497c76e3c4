"""
自动测试打分工具
用于评估学生代码的正确性和给出分数

功能：
1. 自动运行学生代码
2. 检查函数实现和输出
3. 评估代码质量
4. 生成详细的评分报告
"""

import os
import sys
import importlib.util
import traceback
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
import subprocess
import ast
import inspect

class CodeAnalyzer:
    """代码分析器 - 分析代码质量和结构"""
    
    def __init__(self):
        self.quality_metrics = {
            'function_count': 0,
            'class_count': 0,
            'comment_lines': 0,
            'total_lines': 0,
            'complexity_score': 0,
            'docstring_coverage': 0
        }
    
    def analyze_code_structure(self, file_path: str) -> Dict[str, Any]:
        """分析代码结构"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            tree = ast.parse(code)
            
            # 统计函数和类
            functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
            classes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
            
            # 统计行数和注释
            lines = code.split('\n')
            total_lines = len(lines)
            comment_lines = sum(1 for line in lines if line.strip().startswith('#'))
            
            # 检查文档字符串
            docstring_count = 0
            for node in functions + classes:
                if ast.get_docstring(node):
                    docstring_count += 1
            
            docstring_coverage = (docstring_count / max(len(functions) + len(classes), 1)) * 100
            
            return {
                'function_count': len(functions),
                'class_count': len(classes),
                'comment_lines': comment_lines,
                'total_lines': total_lines,
                'docstring_coverage': docstring_coverage,
                'functions': [f.name for f in functions],
                'classes': [c.name for c in classes]
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def calculate_complexity_score(self, file_path: str) -> float:
        """计算代码复杂度分数"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            tree = ast.parse(code)
            complexity = 0
            
            # 简单的复杂度计算
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.While, ast.For)):
                    complexity += 1
                elif isinstance(node, ast.Try):
                    complexity += 2
            
            return min(complexity / 10.0, 1.0)  # 标准化到0-1
            
        except:
            return 0.0

class TestRunner:
    """测试运行器 - 执行各种测试用例"""
    
    def __init__(self):
        self.test_results = []
    
    def run_function_test(self, module, function_name: str, test_cases: List[Dict]) -> Dict[str, Any]:
        """运行函数测试"""
        results = {
            'function_name': function_name,
            'total_tests': len(test_cases),
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
        
        if not hasattr(module, function_name):
            results['error'] = f"函数 {function_name} 未找到"
            return results
        
        func = getattr(module, function_name)
        
        for i, test_case in enumerate(test_cases):
            test_detail = {
                'test_id': i + 1,
                'input': test_case.get('input', {}),
                'expected': test_case.get('expected'),
                'actual': None,
                'passed': False,
                'error': None
            }
            
            try:
                # 执行函数
                if isinstance(test_case['input'], dict):
                    actual_result = func(**test_case['input'])
                elif isinstance(test_case['input'], (list, tuple)):
                    actual_result = func(*test_case['input'])
                else:
                    actual_result = func(test_case['input'])
                
                test_detail['actual'] = actual_result
                
                # 检查结果
                if self._compare_results(actual_result, test_case['expected']):
                    test_detail['passed'] = True
                    results['passed_tests'] += 1
                else:
                    results['failed_tests'] += 1
                    
            except Exception as e:
                test_detail['error'] = str(e)
                results['failed_tests'] += 1
            
            results['test_details'].append(test_detail)
        
        return results
    
    def run_output_test(self, file_path: str, expected_outputs: List[str]) -> Dict[str, Any]:
        """运行输出测试"""
        try:
            # 运行Python文件并捕获输出
            result = subprocess.run([sys.executable, file_path], 
                                  capture_output=True, text=True, timeout=30)
            
            actual_output = result.stdout.strip()
            
            # 检查输出是否包含期望的内容
            matches = 0
            for expected in expected_outputs:
                if expected.lower() in actual_output.lower():
                    matches += 1
            
            return {
                'total_expected': len(expected_outputs),
                'matches': matches,
                'success_rate': matches / len(expected_outputs) if expected_outputs else 0,
                'actual_output': actual_output,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {'error': '程序运行超时'}
        except Exception as e:
            return {'error': str(e)}
    
    def _compare_results(self, actual, expected) -> bool:
        """比较实际结果和期望结果"""
        if type(actual) != type(expected):
            return False
        
        if isinstance(expected, float):
            return abs(actual - expected) < 0.01  # 浮点数比较
        elif isinstance(expected, (list, tuple)):
            if len(actual) != len(expected):
                return False
            return all(self._compare_results(a, e) for a, e in zip(actual, expected))
        else:
            return actual == expected

class GradingSystem:
    """评分系统 - 综合评估和打分"""
    
    def __init__(self):
        self.grading_criteria = {
            'functionality': 0.4,    # 功能实现 40%
            'code_quality': 0.3,     # 代码质量 30%
            'output_correctness': 0.2, # 输出正确性 20%
            'bonus': 0.1             # 额外加分 10%
        }
    
    def grade_submission(self, file_path: str, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """评估学生提交的代码"""
        print(f"🔍 开始评估文件: {file_path}")
        
        # 初始化评分结果
        grade_result = {
            'file_path': file_path,
            'timestamp': datetime.now().isoformat(),
            'total_score': 0,
            'max_score': 100,
            'grade_breakdown': {},
            'detailed_feedback': [],
            'suggestions': []
        }
        
        try:
            # 1. 代码结构分析
            analyzer = CodeAnalyzer()
            code_analysis = analyzer.analyze_code_structure(file_path)
            complexity_score = analyzer.calculate_complexity_score(file_path)
            
            # 2. 功能测试
            test_runner = TestRunner()
            functionality_score = 0
            
            if 'function_tests' in test_config:
                module = self._load_module(file_path)
                if module:
                    for func_test in test_config['function_tests']:
                        func_result = test_runner.run_function_test(
                            module, func_test['function_name'], func_test['test_cases']
                        )
                        
                        func_score = (func_result['passed_tests'] / func_result['total_tests']) * 100
                        functionality_score += func_score * func_test.get('weight', 1.0)
                        
                        grade_result['detailed_feedback'].append({
                            'category': '功能测试',
                            'function': func_test['function_name'],
                            'score': func_score,
                            'details': func_result
                        })
            
            # 3. 输出测试
            output_score = 0
            if 'output_tests' in test_config:
                output_result = test_runner.run_output_test(
                    file_path, test_config['output_tests']['expected_outputs']
                )
                output_score = output_result.get('success_rate', 0) * 100
                
                grade_result['detailed_feedback'].append({
                    'category': '输出测试',
                    'score': output_score,
                    'details': output_result
                })
            
            # 4. 代码质量评估
            quality_score = self._calculate_quality_score(code_analysis, complexity_score)
            
            # 5. 计算总分
            scores = {
                'functionality': functionality_score * self.grading_criteria['functionality'] / 100,
                'code_quality': quality_score * self.grading_criteria['code_quality'],
                'output_correctness': output_score * self.grading_criteria['output_correctness'] / 100,
                'bonus': 0  # 可以根据特殊表现给予加分
            }
            
            total_score = sum(scores.values())
            
            grade_result.update({
                'total_score': round(total_score, 2),
                'grade_breakdown': scores,
                'code_analysis': code_analysis,
                'suggestions': self._generate_suggestions(code_analysis, scores)
            })
            
        except Exception as e:
            grade_result['error'] = str(e)
            grade_result['traceback'] = traceback.format_exc()
        
        return grade_result
    
    def _load_module(self, file_path: str):
        """动态加载Python模块"""
        try:
            spec = importlib.util.spec_from_file_location("student_module", file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return module
        except Exception as e:
            print(f"❌ 模块加载失败: {e}")
            return None
    
    def _calculate_quality_score(self, code_analysis: Dict, complexity_score: float) -> float:
        """计算代码质量分数"""
        if 'error' in code_analysis:
            return 0.0
        
        # 基础分数
        base_score = 50.0
        
        # 文档字符串覆盖率加分
        docstring_bonus = code_analysis.get('docstring_coverage', 0) * 0.2
        
        # 注释比例加分
        comment_ratio = code_analysis.get('comment_lines', 0) / max(code_analysis.get('total_lines', 1), 1)
        comment_bonus = min(comment_ratio * 100, 20)
        
        # 复杂度适中加分
        complexity_bonus = (1 - complexity_score) * 10
        
        # 函数数量合理性
        func_count = code_analysis.get('function_count', 0)
        func_bonus = min(func_count * 5, 20) if func_count > 0 else 0
        
        total_score = base_score + docstring_bonus + comment_bonus + complexity_bonus + func_bonus
        return min(total_score, 100.0)
    
    def _generate_suggestions(self, code_analysis: Dict, scores: Dict) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if scores['functionality'] < 30:
            suggestions.append("🔧 功能实现不完整，请检查所有要求的函数是否正确实现")
        
        if code_analysis.get('docstring_coverage', 0) < 50:
            suggestions.append("📝 建议为函数和类添加文档字符串，提高代码可读性")
        
        if code_analysis.get('comment_lines', 0) / max(code_analysis.get('total_lines', 1), 1) < 0.1:
            suggestions.append("💬 建议添加更多注释，解释复杂的逻辑")
        
        if scores['output_correctness'] < 20:
            suggestions.append("📤 程序输出不符合要求，请检查打印语句和输出格式")
        
        if code_analysis.get('function_count', 0) == 0:
            suggestions.append("🏗️ 建议将代码组织成函数，提高代码的模块化程度")
        
        return suggestions

class ReportGenerator:
    """报告生成器 - 生成详细的评分报告"""
    
    def generate_html_report(self, grade_result: Dict[str, Any], output_path: str):
        """生成HTML格式的评分报告"""
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>代码评分报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { text-align: center; color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px; }
                .score-box { background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }
                .score-number { font-size: 48px; font-weight: bold; }
                .breakdown { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                .breakdown-item { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50; }
                .feedback-section { margin: 20px 0; }
                .feedback-item { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px; }
                .suggestions { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; }
                .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎯 代码评分报告</h1>
                    <p>文件: {file_path}</p>
                    <p>评估时间: {timestamp}</p>
                </div>
                
                <div class="score-box">
                    <div class="score-number">{total_score}</div>
                    <div>总分: {total_score} / {max_score}</div>
                </div>
                
                <div class="breakdown">
                    {breakdown_html}
                </div>
                
                <div class="feedback-section">
                    <h3>📋 详细反馈</h3>
                    {feedback_html}
                </div>
                
                <div class="suggestions">
                    <h3>💡 改进建议</h3>
                    {suggestions_html}
                </div>
            </div>
        </body>
        </html>
        """
        
        # 生成分数分解HTML
        breakdown_html = ""
        for category, score in grade_result.get('grade_breakdown', {}).items():
            breakdown_html += f"""
            <div class="breakdown-item">
                <h4>{category}</h4>
                <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">{score:.1f}</div>
            </div>
            """
        
        # 生成反馈HTML
        feedback_html = ""
        for feedback in grade_result.get('detailed_feedback', []):
            feedback_html += f"""
            <div class="feedback-item">
                <strong>{feedback.get('category', 'N/A')}</strong>
                <p>分数: {feedback.get('score', 0):.1f}</p>
            </div>
            """
        
        # 生成建议HTML
        suggestions_html = "<ul>"
        for suggestion in grade_result.get('suggestions', []):
            suggestions_html += f"<li>{suggestion}</li>"
        suggestions_html += "</ul>"
        
        # 填充模板
        html_content = html_template.format(
            file_path=grade_result.get('file_path', 'N/A'),
            timestamp=grade_result.get('timestamp', 'N/A'),
            total_score=grade_result.get('total_score', 0),
            max_score=grade_result.get('max_score', 100),
            breakdown_html=breakdown_html,
            feedback_html=feedback_html,
            suggestions_html=suggestions_html
        )
        
        # 保存HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"📄 HTML报告已生成: {output_path}")
    
    def generate_json_report(self, grade_result: Dict[str, Any], output_path: str):
        """生成JSON格式的评分报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(grade_result, f, ensure_ascii=False, indent=2)
        
        print(f"📄 JSON报告已生成: {output_path}")

def create_test_configs():
    """创建各难度级别的测试配置"""
    return {
        'easy': {
            'currency_api': {
                'output_tests': {
                    'expected_outputs': ['汇率', '人民币', '日元', '成功获取数据', '货币转换']
                },
                'code_requirements': {
                    'required_imports': ['requests', 'json'],
                    'required_functions': ['get_exchange_rate', 'convert_currency', 'main'],
                    'min_functions': 3
                }
            },
            'matplotlib': {
                'output_tests': {
                    'expected_outputs': ['图表', '绘制', '显示', '汇率变化', '对比']
                },
                'code_requirements': {
                    'required_imports': ['matplotlib.pyplot'],
                    'required_functions': ['create_exchange_rate_chart', 'create_currency_comparison'],
                    'min_functions': 2
                }
            }
        },
        'medium': {
            'currency_api': {
                'function_tests': [
                    {
                        'function_name': 'validate_currency',
                        'test_cases': [
                            {'input': ['USD'], 'expected': True},
                            {'input': ['INVALID'], 'expected': False}
                        ],
                        'weight': 0.2
                    }
                ],
                'code_requirements': {
                    'required_classes': ['CurrencyConverter'],
                    'min_methods': 5
                }
            }
        },
        'hard': {
            'currency_api': {
                'code_requirements': {
                    'required_classes': ['AdvancedCurrencyAPI', 'CurrencyPortfolio', 'CurrencyDataAnalyzer'],
                    'min_methods': 15,
                    'required_features': ['caching', 'error_handling', 'logging']
                }
            }
        }
    }

def batch_grade_submissions(submissions_dir: str, difficulty: str):
    """批量评估学生提交的作业"""
    print(f"📚 开始批量评估 {difficulty} 难度作业")
    print("=" * 50)

    test_configs = create_test_configs()
    grading_system = GradingSystem()
    report_generator = ReportGenerator()

    results_summary = []

    # 遍历提交目录
    for filename in os.listdir(submissions_dir):
        if filename.endswith('.py'):
            file_path = os.path.join(submissions_dir, filename)
            student_name = filename.replace('.py', '')

            print(f"📝 评估学生: {student_name}")

            # 确定测试配置
            if 'api' in filename.lower():
                config = test_configs.get(difficulty, {}).get('currency_api', {})
            elif 'matplotlib' in filename.lower():
                config = test_configs.get(difficulty, {}).get('matplotlib', {})
            else:
                config = test_configs.get(difficulty, {}).get('currency_api', {})

            # 执行评估
            result = grading_system.grade_submission(file_path, config)
            result['student_name'] = student_name

            # 生成个人报告
            html_path = f"reports/{student_name}_report.html"
            json_path = f"reports/{student_name}_report.json"

            os.makedirs("reports", exist_ok=True)
            report_generator.generate_html_report(result, html_path)
            report_generator.generate_json_report(result, json_path)

            results_summary.append({
                'student': student_name,
                'score': result.get('total_score', 0),
                'file': filename
            })

            print(f"✅ {student_name}: {result.get('total_score', 0):.1f}/100")

    # 生成汇总报告
    generate_summary_report(results_summary, difficulty)

def generate_summary_report(results: List[Dict], difficulty: str):
    """生成班级汇总报告"""
    if not results:
        return

    # 计算统计信息
    scores = [r['score'] for r in results]
    avg_score = sum(scores) / len(scores)
    max_score = max(scores)
    min_score = min(scores)

    # 排序
    results.sort(key=lambda x: x['score'], reverse=True)

    # 生成HTML汇总报告
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>{difficulty.upper()}难度 - 班级成绩汇总</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .summary {{ background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
            th {{ background-color: #4CAF50; color: white; }}
            .high-score {{ background-color: #d4edda; }}
            .medium-score {{ background-color: #fff3cd; }}
            .low-score {{ background-color: #f8d7da; }}
        </style>
    </head>
    <body>
        <h1>📊 {difficulty.upper()}难度 - 班级成绩汇总</h1>

        <div class="summary">
            <h3>📈 统计信息</h3>
            <p><strong>参与人数:</strong> {len(results)}</p>
            <p><strong>平均分:</strong> {avg_score:.1f}</p>
            <p><strong>最高分:</strong> {max_score:.1f}</p>
            <p><strong>最低分:</strong> {min_score:.1f}</p>
        </div>

        <table>
            <tr>
                <th>排名</th>
                <th>学生姓名</th>
                <th>文件名</th>
                <th>得分</th>
                <th>等级</th>
            </tr>
    """

    for i, result in enumerate(results, 1):
        score = result['score']
        if score >= 80:
            grade_class = "high-score"
            grade = "优秀"
        elif score >= 60:
            grade_class = "medium-score"
            grade = "良好"
        else:
            grade_class = "low-score"
            grade = "需改进"

        html_content += f"""
            <tr class="{grade_class}">
                <td>{i}</td>
                <td>{result['student']}</td>
                <td>{result['file']}</td>
                <td>{score:.1f}</td>
                <td>{grade}</td>
            </tr>
        """

    html_content += """
        </table>
    </body>
    </html>
    """

    # 保存汇总报告
    summary_path = f"reports/{difficulty}_summary.html"
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"📋 班级汇总报告已生成: {summary_path}")

def main():
    """主函数 - 演示自动评分系统"""
    print("🎯 自动测试打分工具")
    print("=" * 50)

    # 显示使用说明
    print("使用方法:")
    print("1. 单个文件评估: python auto_grader.py --file <文件路径> --difficulty <难度>")
    print("2. 批量评估: python auto_grader.py --batch <目录路径> --difficulty <难度>")
    print("3. 演示模式: python auto_grader.py --demo")
    print()

    import sys
    if len(sys.argv) > 1:
        if '--demo' in sys.argv:
            # 演示模式
            demo_single_file_grading()
        elif '--batch' in sys.argv:
            # 批量模式
            try:
                batch_idx = sys.argv.index('--batch')
                diff_idx = sys.argv.index('--difficulty')
                submissions_dir = sys.argv[batch_idx + 1]
                difficulty = sys.argv[diff_idx + 1]
                batch_grade_submissions(submissions_dir, difficulty)
            except (ValueError, IndexError):
                print("❌ 参数错误，请检查命令格式")
        elif '--file' in sys.argv:
            # 单文件模式
            try:
                file_idx = sys.argv.index('--file')
                diff_idx = sys.argv.index('--difficulty')
                file_path = sys.argv[file_idx + 1]
                difficulty = sys.argv[diff_idx + 1]
                grade_single_file(file_path, difficulty)
            except (ValueError, IndexError):
                print("❌ 参数错误，请检查命令格式")
    else:
        # 默认演示模式
        demo_single_file_grading()

def demo_single_file_grading():
    """演示单文件评分"""
    print("🎭 演示模式 - 单文件评分")

    # 创建测试配置
    test_configs = create_test_configs()
    grading_system = GradingSystem()
    report_generator = ReportGenerator()

    # 测试简单难度文件
    easy_files = [
        "../easy/currency_api_easy.py",
        "../easy/matplotlib_easy.py"
    ]

    for file_path in easy_files:
        if os.path.exists(file_path):
            print(f"\n📝 评估文件: {file_path}")

            # 选择配置
            if 'api' in file_path:
                config = test_configs['easy']['currency_api']
            else:
                config = test_configs['easy']['matplotlib']

            result = grading_system.grade_submission(file_path, config)

            # 生成报告
            base_name = os.path.basename(file_path).replace('.py', '')
            html_path = f"{base_name}_demo_report.html"

            report_generator.generate_html_report(result, html_path)

            print(f"✅ 评分完成，总分: {result.get('total_score', 0):.1f}/100")
            print(f"📄 报告已生成: {html_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")

def grade_single_file(file_path: str, difficulty: str):
    """评估单个文件"""
    print(f"📝 评估文件: {file_path} (难度: {difficulty})")

    test_configs = create_test_configs()
    grading_system = GradingSystem()
    report_generator = ReportGenerator()

    # 选择配置
    if 'api' in file_path.lower():
        config = test_configs.get(difficulty, {}).get('currency_api', {})
    else:
        config = test_configs.get(difficulty, {}).get('matplotlib', {})

    result = grading_system.grade_submission(file_path, config)

    # 生成报告
    base_name = os.path.basename(file_path).replace('.py', '')
    html_path = f"{base_name}_report.html"
    json_path = f"{base_name}_report.json"

    report_generator.generate_html_report(result, html_path)
    report_generator.generate_json_report(result, json_path)

    print(f"✅ 评分完成，总分: {result.get('total_score', 0):.1f}/100")
    print(f"📄 报告已生成: {html_path}")

if __name__ == "__main__":
    main()
