{"easy": {"currency_api_easy": {"description": "简单难度汇率API测试配置", "output_tests": {"expected_outputs": ["开始获取汇率信息", "成功获取数据", "汇率", "人民币", "日元", "货币转换计算器", "程序运行完成"]}, "code_requirements": {"required_imports": ["requests", "json"], "required_functions": ["get_exchange_rate", "convert_currency", "main"], "min_functions": 3, "required_keywords": ["response.json()", "rates", "CNY"]}, "scoring_weights": {"functionality": 0.4, "code_quality": 0.3, "output_correctness": 0.2, "bonus": 0.1}}, "matplotlib_easy": {"description": "简单难度Matplotlib测试配置", "output_tests": {"expected_outputs": ["开始创建汇率图表", "数据准备完成", "正在绘制图表", "图表显示完成", "创建货币转换对比图", "所有图表演示完成"]}, "code_requirements": {"required_imports": ["matplotlib.pyplot"], "required_functions": ["create_exchange_rate_chart", "create_currency_comparison", "main"], "min_functions": 3, "required_keywords": ["plt.plot", "plt.title", "plt.xlabel", "plt.ylabel", "plt.grid", "plt.show"]}}}, "medium": {"currency_api_medium": {"description": "中等难度汇率API测试配置", "function_tests": [{"function_name": "validate_currency", "test_cases": [{"input": ["USD"], "expected": true}, {"input": ["EUR"], "expected": true}, {"input": ["INVALID"], "expected": false}, {"input": ["xyz"], "expected": false}], "weight": 0.15}, {"function_name": "convert_amount", "test_cases": [{"input": [100, "USD", "CNY"], "expected": "float"}, {"input": [0, "EUR", "JPY"], "expected": 0}], "weight": 0.25}], "code_requirements": {"required_classes": ["CurrencyConverter"], "required_methods": ["get_current_rate", "get_historical_rates", "convert_amount", "validate_currency"], "min_methods": 4, "required_features": ["error_handling", "user_interaction"]}}, "matplotlib_medium": {"description": "中等难度Matplotlib测试配置", "code_requirements": {"required_classes": ["ExchangeRateVisualizer"], "required_methods": ["create_line_chart", "create_bar_chart", "create_comparison_chart", "create_pie_chart"], "min_methods": 4, "required_features": ["multiple_chart_types", "data_customization", "interactive_elements"]}}}, "hard": {"currency_api_hard": {"description": "困难难度汇率API测试配置", "code_requirements": {"required_classes": ["AdvancedCurrencyAPI", "CurrencyPortfolio", "CurrencyDataAnalyzer"], "min_methods": 15, "required_features": ["caching_system", "batch_processing", "error_handling", "logging", "type_annotations", "documentation", "performance_optimization"], "advanced_requirements": {"async_support": false, "database_integration": false, "api_rate_limiting": true, "data_validation": true}}, "performance_requirements": {"max_response_time": 5.0, "memory_efficiency": true, "concurrent_requests": 10}}, "matplotlib_hard": {"description": "困难难度Matplotlib测试配置", "code_requirements": {"required_classes": ["AdvancedVisualizationEngine", "TechnicalAnalysisVisualizer", "ReportGenerator", "DataStreamSimulator"], "min_methods": 20, "required_features": ["interactive_charts", "animation_support", "3d_visualization", "report_generation", "theme_customization", "export_functionality"], "advanced_requirements": {"real_time_updates": true, "widget_integration": true, "professional_styling": true, "performance_optimization": true}}}}, "common_evaluation_criteria": {"code_quality_metrics": {"pep8_compliance": 10, "docstring_coverage": 15, "comment_ratio": 10, "function_complexity": 10, "error_handling": 15, "code_organization": 10}, "functionality_metrics": {"core_features": 40, "edge_cases": 20, "user_experience": 15, "performance": 10, "extensibility": 15}, "bonus_criteria": {"innovation": 5, "extra_features": 3, "code_elegance": 2}}, "grading_scale": {"A": {"min": 90, "max": 100, "description": "优秀 - 完全掌握所有概念"}, "B": {"min": 80, "max": 89, "description": "良好 - 掌握大部分概念"}, "C": {"min": 70, "max": 79, "description": "中等 - 掌握基本概念"}, "D": {"min": 60, "max": 69, "description": "及格 - 部分掌握概念"}, "F": {"min": 0, "max": 59, "description": "不及格 - 需要重新学习"}}}