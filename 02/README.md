# 汇率API与Matplotlib教学项目

## 项目简介
这是一个专为小学生设计的Python编程教学项目，通过学习汇率API的使用和Matplotlib绘图来掌握基础编程概念。

## 学习目标
1. **汇率API使用**: 学会从JSON数据中提取目标货币汇率
2. **Matplotlib绘图**: 掌握基本的数据可视化技能

## 项目结构
```
currency_exchange_tutorial/
├── easy/           # 简单难度（填空题）
├── medium/         # 中等难度（部分框架）
├── hard/           # 困难难度（函数框架）
├── testing/        # 自动测试工具
├── utils/          # 辅助工具
├── examples/       # 示例和演示
└── README.md       # 项目说明
```

## 三种难度说明

### 🟢 简单难度 (Easy)
- **适合对象**: 刚接触Python的小学生
- **形式**: 填空题，关键代码已提供
- **学习重点**: 理解代码结构和基本概念

### 🟡 中等难度 (Medium)  
- **适合对象**: 有一定Python基础的学生
- **形式**: 提供部分代码框架，需要完成主要逻辑
- **学习重点**: 独立思考和问题解决

### 🔴 困难难度 (Hard)
- **适合对象**: 有较好编程基础的学生
- **形式**: 只提供函数框架，需要实现完整功能
- **学习重点**: 综合运用和创新思维

## 使用方法
1. 根据学生水平选择对应难度的文件夹
2. 阅读任务说明和要求
3. 完成代码编写
4. 使用测试工具验证结果

## API说明
- **单次汇率查询**: `https://api.frankfurter.app/latest?from=JPY&to=CNY`
- **历史汇率数据**: `https://api.frankfurter.dev/v1/2025-06-01..?base=CNY&symbols=JPY`

## 重点知识点
- JSON数据解析
- HTTP请求处理
- Matplotlib基础绘图函数
- 数据处理和可视化
