"""
完整示例集合 - 展示各种功能的完整实现
"""

import requests
import json
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import random

# ============================================================================
# 简单难度完整示例
# ============================================================================

def simple_currency_api_example():
    """简单难度：汇率API使用完整示例"""
    print("简单难度：汇率API使用示例")
    print("=" * 40)
    
    def get_exchange_rate():
        """获取日元到人民币的汇率"""
        print("🌟 开始获取汇率信息...")
        
        url = "https://api.frankfurter.app/latest?from=JPY&to=CNY"
        
        try:
            print("📡 正在连接汇率API...")
            response = requests.get(url)
            
            if response.status_code == 200:
                print("✅ 成功获取数据！")
                
                # 解析JSON数据
                data = response.json()
                print("📊 原始数据：", data)
                
                # 提取汇率
                rates = data["rates"]
                cny_rate = rates["CNY"]
                
                print(f"💰 1日元 = {cny_rate}人民币")
                return cny_rate
            else:
                print("❌ 获取数据失败，状态码：", response.status_code)
                return None
                
        except Exception as e:
            print("🚨 发生错误：", str(e))
            return None
    
    def convert_currency(amount, rate):
        """货币转换计算"""
        converted_amount = amount * rate
        print(f"💴 {amount}日元 = {converted_amount:.2f}人民币")
        return converted_amount
    
    # 执行示例
    rate = get_exchange_rate()
    if rate:
        print("\n" + "=" * 40)
        print("💱 货币转换计算器")
        
        test_amounts = [100, 500, 1000, 5000]
        for amount in test_amounts:
            convert_currency(amount, rate)
    
    print("\n🎉 简单示例完成！")

def simple_matplotlib_example():
    """简单难度：Matplotlib绘图完整示例"""
    print("\n简单难度：Matplotlib绘图示例")
    print("=" * 40)
    
    def create_exchange_rate_chart():
        """创建汇率变化图表"""
        print("📈 开始创建汇率图表...")
        
        # 模拟数据
        dates = ['6-19', '6-20', '6-21', '6-22', '6-23', '6-24', '6-25']
        rates = [0.048, 0.049, 0.047, 0.050, 0.049, 0.048, 0.049]
        
        print("📊 数据准备完成：")
        print("日期：", dates)
        print("汇率：", rates)
        
        # 创建图表
        plt.plot(dates, rates)
        plt.title("日元到人民币汇率变化")
        plt.xlabel("日期")
        plt.ylabel("汇率 (CNY)")
        plt.grid(True)
        plt.show()
        
        print("✅ 图表显示完成！")
    
    def create_currency_comparison():
        """创建货币转换对比图"""
        print("\n💱 创建货币转换对比图...")
        
        jpy_amounts = [100, 500, 1000, 2000, 5000, 10000]
        cny_amounts = [4.9, 24.5, 49.0, 98.0, 245.0, 490.0]
        
        plt.bar(jpy_amounts, cny_amounts)
        plt.title("日元转人民币金额对比")
        plt.xlabel("日元金额")
        plt.ylabel("人民币金额")
        plt.grid(True)
        plt.show()
        
        print("✅ 对比图显示完成！")
    
    # 执行示例
    create_exchange_rate_chart()
    create_currency_comparison()
    print("🎉 绘图示例完成！")

# ============================================================================
# 中等难度完整示例
# ============================================================================

class CurrencyConverter:
    """中等难度：货币转换器完整实现"""
    
    def __init__(self):
        self.base_url = "https://api.frankfurter.app"
        self.supported_currencies = ["USD", "EUR", "JPY", "GBP", "CNY", "KRW"]
    
    def get_current_rate(self, from_currency, to_currency):
        """获取当前汇率"""
        url = f"{self.base_url}/latest?from={from_currency}&to={to_currency}"
        
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data["rates"][to_currency]
            else:
                print(f"API请求失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取汇率失败: {e}")
            return None
    
    def get_historical_rates(self, base_currency, target_currency, days=7):
        """获取历史汇率数据"""
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
        url = f"{self.base_url}/{start_date}..?base={base_currency}&symbols={target_currency}"
        
        try:
            response = requests.get(url, timeout=15)
            if response.status_code == 200:
                data = response.json()
                return data.get("rates", {})
            else:
                print(f"历史数据获取失败，状态码: {response.status_code}")
                return {}
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return {}
    
    def convert_amount(self, amount, from_currency, to_currency):
        """转换货币金额"""
        rate = self.get_current_rate(from_currency, to_currency)
        if rate:
            return amount * rate
        return None
    
    def validate_currency(self, currency_code):
        """验证货币代码"""
        return currency_code.upper() in self.supported_currencies

def medium_currency_example():
    """中等难度完整示例"""
    print("\n🟡 中等难度：货币转换器示例")
    print("=" * 40)
    
    converter = CurrencyConverter()
    
    # 演示功能
    print("💱 货币转换器演示")
    print(f"支持的货币: {', '.join(converter.supported_currencies)}")
    
    # 测试汇率查询
    rate = converter.get_current_rate("USD", "CNY")
    if rate:
        print(f"✅ USD/CNY 汇率: {rate}")
    
    # 测试金额转换
    converted = converter.convert_amount(100, "USD", "CNY")
    if converted:
        print(f"💰 100 USD = {converted:.2f} CNY")
    
    # 测试历史数据
    historical = converter.get_historical_rates("USD", "CNY", 5)
    if historical:
        print("📈 最近5天历史汇率:")
        for date, rates in list(historical.items())[-3:]:
            print(f"  {date}: {rates.get('CNY', 'N/A')}")
    
    print("🎉 中等难度示例完成！")

class ExchangeRateVisualizer:
    """中等难度：汇率可视化器"""
    
    def __init__(self):
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    
    def create_line_chart(self, dates, rates, title, currency_pair):
        """创建汇率趋势线图"""
        plt.figure(figsize=(10, 6))
        plt.plot(dates, rates, color='blue', linewidth=2, marker='o', markersize=6)
        plt.title(title, fontsize=16)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel(f'汇率 ({currency_pair})', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()
    
    def create_bar_chart(self, currencies, rates, base_currency):
        """创建多货币汇率对比柱状图"""
        plt.figure(figsize=(10, 6))
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        bars = plt.bar(currencies, rates, color=colors[:len(currencies)])
        
        # 添加数值标签
        for i, (bar, rate) in enumerate(zip(bars, rates)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{rate:.3f}', ha='center', va='bottom')
        
        plt.title(f'多货币汇率对比 (基准: {base_currency})', fontsize=16)
        plt.xlabel('货币', fontsize=12)
        plt.ylabel('汇率', fontsize=12)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

def medium_matplotlib_example():
    """中等难度Matplotlib示例"""
    print("\n🟡 中等难度：高级图表示例")
    print("=" * 40)
    
    visualizer = ExchangeRateVisualizer()
    
    # 生成示例数据
    dates = [(datetime.now() - timedelta(days=i)).strftime('%m-%d') for i in range(7, 0, -1)]
    rates = [6.5 + random.uniform(-0.2, 0.2) for _ in range(7)]
    
    # 创建线图
    print("📈 创建汇率趋势图...")
    visualizer.create_line_chart(dates, rates, "USD/CNY 汇率趋势", "CNY")
    
    # 创建柱状图
    print("📊 创建多货币对比图...")
    currencies = ['EUR', 'JPY', 'GBP', 'KRW']
    multi_rates = [0.85, 110.5, 0.75, 1200.8]
    visualizer.create_bar_chart(currencies, multi_rates, 'USD')
    
    print("🎉 高级图表示例完成！")

# ============================================================================
# 主函数
# ============================================================================

def main():
    """运行所有示例"""
    print("🎯 汇率API与Matplotlib完整示例集合")
    print("=" * 60)
    print("这个程序将演示各个难度级别的完整实现")
    
    try:
        # 简单难度示例
        simple_currency_api_example()
        simple_matplotlib_example()
        
        # 中等难度示例
        medium_currency_example()
        medium_matplotlib_example()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例演示完成！")
        print("\n💡 学习要点总结:")
        print("✅ API调用：requests.get() → response.json()")
        print("✅ 数据提取：从JSON中获取特定字段")
        print("✅ 错误处理：try-except捕获异常")
        print("✅ 图表绘制：plt.plot(), plt.bar(), plt.show()")
        print("✅ 图表美化：标题、标签、网格、颜色")
        print("✅ 面向对象：类的设计和方法实现")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        print("💡 这可能是网络连接问题，请检查网络后重试")

if __name__ == "__main__":
    main()
